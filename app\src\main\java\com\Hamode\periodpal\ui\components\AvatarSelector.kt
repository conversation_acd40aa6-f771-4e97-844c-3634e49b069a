package com.Hamode.periodpal.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.Hamode.periodpal.ui.theme.*

data class AvatarOption(
    val id: String,
    val icon: ImageVector,
    val backgroundColor: Color,
    val iconColor: Color = Color.White,
    val name: String
)

object AvatarOptions {
    val avatars = listOf(
        // Female-themed avatars
        AvatarOption("female_1", Icons.Default.Person, RosePink40, Color.White, "Classic"),
        AvatarOption("female_2", Icons.Default.Face, Color(0xFFE91E63), Color.White, "Friendly"),
        AvatarOption("female_3", Icons.Default.Favorite, Color(0xFFAD1457), Color.White, "Heart"),
        AvatarOption("female_4", Icons.Default.Star, Color(0xFFF06292), Color.White, "Star"),
        
        // Nature-themed
        AvatarOption("nature_1", Icons.Default.LocalFlorist, FertileGreen, Color.White, "Flower"),
        AvatarOption("nature_2", Icons.Default.Eco, Color(0xFF4CAF50), Color.White, "Eco"),
        AvatarOption("nature_3", Icons.Default.Park, Color(0xFF8BC34A), Color.White, "Nature"),
        AvatarOption("nature_4", Icons.Default.WbSunny, Color(0xFFFFEB3B), Color.White, "Sunny"),
        
        // Wellness-themed
        AvatarOption("wellness_1", Icons.Default.SelfImprovement, Color(0xFF9C27B0), Color.White, "Wellness"),
        AvatarOption("wellness_2", Icons.Default.Spa, Color(0xFF673AB7), Color.White, "Spa"),
        AvatarOption("wellness_3", Icons.Default.FitnessCenter, Color(0xFF3F51B5), Color.White, "Fitness"),
        AvatarOption("wellness_4", Icons.Default.Healing, Color(0xFF2196F3), Color.White, "Healing"),
        
        // Fun & Creative
        AvatarOption("fun_1", Icons.Default.Palette, Color(0xFF00BCD4), Color.White, "Creative"),
        AvatarOption("fun_2", Icons.Default.MusicNote, Color(0xFF009688), Color.White, "Music"),
        AvatarOption("fun_3", Icons.Default.Celebration, Color(0xFFFF9800), Color.White, "Celebration"),
        AvatarOption("fun_4", Icons.Default.Diamond, Color(0xFFFF5722), Color.White, "Diamond"),
        
        // Professional
        AvatarOption("prof_1", Icons.Default.Work, Color(0xFF795548), Color.White, "Professional"),
        AvatarOption("prof_2", Icons.Default.School, Color(0xFF607D8B), Color.White, "Academic"),
        AvatarOption("prof_3", Icons.Default.BusinessCenter, Color(0xFF424242), Color.White, "Business"),
        AvatarOption("prof_4", Icons.Default.Psychology, Color(0xFF6D4C41), Color.White, "Mindful")
    )
    
    fun getAvatarById(id: String): AvatarOption? {
        return avatars.find { it.id == id }
    }
    
    fun getDefaultAvatar(): AvatarOption {
        return avatars.first()
    }
}

@Composable
fun AvatarDisplay(
    avatarId: String,
    size: Int = 80,
    modifier: Modifier = Modifier
) {
    val avatar = AvatarOptions.getAvatarById(avatarId) ?: AvatarOptions.getDefaultAvatar()
    
    Box(
        modifier = modifier
            .size(size.dp)
            .clip(CircleShape)
            .background(avatar.backgroundColor),
        contentAlignment = Alignment.Center
    ) {
        Icon(
            imageVector = avatar.icon,
            contentDescription = avatar.name,
            tint = avatar.iconColor,
            modifier = Modifier.size((size * 0.5).dp)
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AvatarSelectorDialog(
    currentAvatarId: String,
    onAvatarSelected: (String) -> Unit,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        modifier = modifier
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .heightIn(max = 600.dp),
            shape = MaterialTheme.shapes.large,
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface
            )
        ) {
            Column(
                modifier = Modifier.padding(24.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // Header
                Text(
                    text = "Choose Your Avatar",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSurface
                )
                
                Text(
                    text = "Select a profile picture that represents you",
                    fontSize = 14.sp,
                    color = DarkGray
                )
                
                // Avatar Grid
                LazyVerticalGrid(
                    columns = GridCells.Fixed(4),
                    horizontalArrangement = Arrangement.spacedBy(12.dp),
                    verticalArrangement = Arrangement.spacedBy(12.dp),
                    modifier = Modifier.heightIn(max = 400.dp)
                ) {
                    items(AvatarOptions.avatars) { avatar ->
                        AvatarOptionItem(
                            avatar = avatar,
                            isSelected = avatar.id == currentAvatarId,
                            onClick = { 
                                onAvatarSelected(avatar.id)
                                onDismiss()
                            }
                        )
                    }
                }
                
                // Buttons
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.End,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    TextButton(onClick = onDismiss) {
                        Text("Cancel")
                    }
                }
            }
        }
    }
}

@Composable
private fun AvatarOptionItem(
    avatar: AvatarOption,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.clickable { onClick() },
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        Box(
            modifier = Modifier
                .size(60.dp)
                .clip(CircleShape)
                .background(avatar.backgroundColor)
                .then(
                    if (isSelected) {
                        Modifier.border(3.dp, RosePink40, CircleShape)
                    } else {
                        Modifier.border(2.dp, Color.Transparent, CircleShape)
                    }
                ),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = avatar.icon,
                contentDescription = avatar.name,
                tint = avatar.iconColor,
                modifier = Modifier.size(30.dp)
            )
        }
        
        Text(
            text = avatar.name,
            fontSize = 10.sp,
            color = if (isSelected) RosePink40 else DarkGray,
            fontWeight = if (isSelected) FontWeight.SemiBold else FontWeight.Normal
        )
    }
}

@Composable
fun AvatarSelector(
    currentAvatarId: String,
    onAvatarSelected: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    var showDialog by remember { mutableStateOf(false) }
    
    Box(
        modifier = modifier.size(80.dp)
    ) {
        // Current Avatar Display
        AvatarDisplay(
            avatarId = currentAvatarId,
            size = 80,
            modifier = Modifier.clickable { showDialog = true }
        )
        
        // Camera/Edit button
        Box(
            modifier = Modifier
                .size(24.dp)
                .clip(CircleShape)
                .background(RosePink40)
                .align(Alignment.BottomEnd)
                .clickable { showDialog = true },
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = Icons.Default.Edit,
                contentDescription = "Change Avatar",
                tint = Color.White,
                modifier = Modifier.size(12.dp)
            )
        }
    }
    
    // Avatar Selection Dialog
    if (showDialog) {
        AvatarSelectorDialog(
            currentAvatarId = currentAvatarId,
            onAvatarSelected = onAvatarSelected,
            onDismiss = { showDialog = false }
        )
    }
}
