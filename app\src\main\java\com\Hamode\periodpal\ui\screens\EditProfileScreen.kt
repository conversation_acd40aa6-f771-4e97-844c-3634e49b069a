package com.Hamode.periodpal.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.Hamode.periodpal.data.models.UserProfile
import com.Hamode.periodpal.ui.theme.*
import com.Hamode.periodpal.ui.viewmodel.ProfileViewModel
import com.Hamode.periodpal.ui.components.AvatarSelector
import java.time.LocalDate
import java.time.format.DateTimeFormatter

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun EditProfileScreen(
    onNavigateBack: () -> Unit,
    modifier: Modifier = Modifier,
    profileViewModel: ProfileViewModel = viewModel()
) {
    val userProfile by profileViewModel.userProfile.collectAsState()
    val uiState by profileViewModel.uiState.collectAsState()

    var displayName by remember { mutableStateOf("") }
    var averageCycleLength by remember { mutableStateOf("28") }
    var averagePeriodLength by remember { mutableStateOf("5") }
    var dateOfBirth by remember { mutableStateOf("") }
    var selectedAvatarId by remember { mutableStateOf("female_1") }

    // Initialize form with current profile data
    LaunchedEffect(userProfile) {
        userProfile?.let { profile ->
            displayName = profile.displayName
            averageCycleLength = profile.averageCycleLength.toString()
            averagePeriodLength = profile.averagePeriodLength.toString()
            dateOfBirth = profile.dateOfBirth?.format(DateTimeFormatter.ISO_LOCAL_DATE) ?: ""
            selectedAvatarId = profile.avatarId
        }
    }

    // Show success message and navigate back
    LaunchedEffect(uiState.showSuccessMessage) {
        if (uiState.showSuccessMessage) {
            kotlinx.coroutines.delay(1000)
            onNavigateBack()
        }
    }

    Column(
        modifier = modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.background)
            .verticalScroll(rememberScrollState())
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // Header
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(onClick = onNavigateBack) {
                Icon(
                    imageVector = Icons.Default.ArrowBack,
                    contentDescription = "Back",
                    tint = RosePink40
                )
            }
            Text(
                text = "Edit Profile",
                fontSize = 24.sp,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onBackground,
                modifier = Modifier.weight(1f)
            )
        }

        Spacer(modifier = Modifier.height(8.dp))

        // Avatar Selection Card
        Card(
            modifier = Modifier.fillMaxWidth(),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface
            ),
            elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
        ) {
            Column(
                modifier = Modifier.padding(20.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                Text(
                    text = "Profile Avatar",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = MaterialTheme.colorScheme.onSurface
                )

                AvatarSelector(
                    currentAvatarId = selectedAvatarId,
                    onAvatarSelected = { selectedAvatarId = it }
                )

                Text(
                    text = "Tap to change your profile picture",
                    fontSize = 14.sp,
                    color = DarkGray
                )
            }
        }

        // Form Card
        Card(
            modifier = Modifier.fillMaxWidth(),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface
            ),
            elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
        ) {
            Column(
                modifier = Modifier.padding(20.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // Display Name
                OutlinedTextField(
                    value = displayName,
                    onValueChange = { displayName = it },
                    label = { Text("Display Name") },
                    leadingIcon = {
                        Icon(
                            imageVector = Icons.Default.Person,
                            contentDescription = "Name",
                            tint = RosePink40
                        )
                    },
                    modifier = Modifier.fillMaxWidth(),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = RosePink40,
                        focusedLabelColor = RosePink40
                    )
                )

                // Date of Birth
                OutlinedTextField(
                    value = dateOfBirth,
                    onValueChange = { dateOfBirth = it },
                    label = { Text("Date of Birth (YYYY-MM-DD)") },
                    leadingIcon = {
                        Icon(
                            imageVector = Icons.Default.CalendarToday,
                            contentDescription = "Date of Birth",
                            tint = RosePink40
                        )
                    },
                    placeholder = { Text("1990-01-01") },
                    modifier = Modifier.fillMaxWidth(),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = RosePink40,
                        focusedLabelColor = RosePink40
                    )
                )

                // Average Cycle Length
                OutlinedTextField(
                    value = averageCycleLength,
                    onValueChange = { averageCycleLength = it },
                    label = { Text("Average Cycle Length (days)") },
                    leadingIcon = {
                        Icon(
                            imageVector = Icons.Default.Schedule,
                            contentDescription = "Cycle Length",
                            tint = RosePink40
                        )
                    },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                    modifier = Modifier.fillMaxWidth(),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = RosePink40,
                        focusedLabelColor = RosePink40
                    )
                )

                // Average Period Length
                OutlinedTextField(
                    value = averagePeriodLength,
                    onValueChange = { averagePeriodLength = it },
                    label = { Text("Average Period Length (days)") },
                    leadingIcon = {
                        Icon(
                            imageVector = Icons.Default.Bloodtype,
                            contentDescription = "Period Length",
                            tint = PeriodRed
                        )
                    },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                    modifier = Modifier.fillMaxWidth(),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = RosePink40,
                        focusedLabelColor = RosePink40
                    )
                )
            }
        }

        // Error Message
        uiState.errorMessage?.let { errorMessage ->
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = ErrorRed.copy(alpha = 0.1f)
                )
            ) {
                Text(
                    text = errorMessage,
                    color = ErrorRed,
                    modifier = Modifier.padding(16.dp)
                )
            }
        }

        // Save Button
        Button(
            onClick = {
                val updatedProfile = userProfile?.copy(
                    displayName = displayName.trim(),
                    avatarId = selectedAvatarId,
                    averageCycleLength = averageCycleLength.toIntOrNull() ?: 28,
                    averagePeriodLength = averagePeriodLength.toIntOrNull() ?: 5,
                    dateOfBirth = if (dateOfBirth.isNotBlank()) {
                        try {
                            LocalDate.parse(dateOfBirth)
                        } catch (e: Exception) {
                            null
                        }
                    } else null
                ) ?: return@Button

                profileViewModel.updateUserProfile(updatedProfile)
            },
            modifier = Modifier
                .fillMaxWidth()
                .height(56.dp),
            colors = ButtonDefaults.buttonColors(
                containerColor = RosePink40
            ),
            shape = RoundedCornerShape(12.dp),
            enabled = !uiState.isLoading
        ) {
            if (uiState.isLoading) {
                CircularProgressIndicator(
                    color = Color.White,
                    modifier = Modifier.size(20.dp)
                )
            } else {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Save,
                        contentDescription = "Save",
                        tint = Color.White
                    )
                    Text(
                        text = "Save Changes",
                        color = Color.White,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium
                    )
                }
            }
        }

        // Bottom spacing
        Spacer(modifier = Modifier.height(80.dp))
    }
}
