package com.Hamode.periodpal.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.Hamode.periodpal.ui.theme.*
import com.Hamode.periodpal.ui.viewmodel.ProfileViewModel
import com.Hamode.periodpal.data.models.UserProfile
import com.Hamode.periodpal.ui.components.AvatarSelector

@Composable
fun ProfileScreen(
    onSignOut: () -> Unit = {},
    onNavigateToSecondaryScreen: (com.Hamode.periodpal.ui.navigation.SecondaryDestination) -> Unit = {},
    modifier: Modifier = Modifier,
    profileViewModel: ProfileViewModel = viewModel()
) {
    val uiState by profileViewModel.uiState.collectAsState()
    val userProfile by profileViewModel.userProfile.collectAsState()
    val cycleStatistics by profileViewModel.cycleStatistics.collectAsState()

    // Show success message
    LaunchedEffect(uiState.showSuccessMessage) {
        if (uiState.showSuccessMessage) {
            kotlinx.coroutines.delay(2000)
            profileViewModel.clearSuccessMessage()
        }
    }

    // Show error message
    uiState.errorMessage?.let { errorMessage ->
        LaunchedEffect(errorMessage) {
            // You can show a snackbar here if needed
        }
    }

    // Show success message for export
    LaunchedEffect(uiState.showSuccessMessage) {
        if (uiState.showSuccessMessage) {
            // Show success message for data export
            kotlinx.coroutines.delay(2000)
            profileViewModel.clearSuccessMessage()
        }
    }
    if (uiState.isLoading) {
        Box(
            modifier = modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            CircularProgressIndicator(color = RosePink40)
        }
    } else {
        Column(
            modifier = modifier
                .fillMaxSize()
                .background(MaterialTheme.colorScheme.background)
                .verticalScroll(rememberScrollState())
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Profile Header
            ProfileHeader(
                userProfile = userProfile,
                onEditProfile = { onNavigateToSecondaryScreen(com.Hamode.periodpal.ui.navigation.SecondaryDestination.EDIT_PROFILE) },
                onRefresh = { profileViewModel.refreshData() },
                onAvatarSelected = { avatarId ->
                    userProfile?.let { profile ->
                        val updatedProfile = profile.copy(
                            avatarId = avatarId,
                            updatedAt = java.time.LocalDate.now()
                        )
                        profileViewModel.updateUserProfile(updatedProfile)
                    }
                },
                isLoading = uiState.isLoading,
                modifier = Modifier.fillMaxWidth()
            )

            // Quick Stats
            QuickStatsSection(
                userProfile = userProfile,
                cycleStatistics = cycleStatistics,
                nextPeriodDays = profileViewModel.getNextPeriodDays(),
                modifier = Modifier.fillMaxWidth()
            )

            // Settings Sections
            SettingsSection(
                title = "Account",
                items = listOf(
                    SettingsItem("Personal Information", Icons.Default.Person) {
                        onNavigateToSecondaryScreen(com.Hamode.periodpal.ui.navigation.SecondaryDestination.EDIT_PROFILE)
                    },
                    SettingsItem("Cycle Settings", Icons.Default.Settings) {
                        onNavigateToSecondaryScreen(com.Hamode.periodpal.ui.navigation.SecondaryDestination.CYCLE_SETTINGS)
                    },
                    SettingsItem("Notifications", Icons.Default.Notifications) {
                        onNavigateToSecondaryScreen(com.Hamode.periodpal.ui.navigation.SecondaryDestination.NOTIFICATIONS)
                    }
                )
            )

            SettingsSection(
                title = "Health & Privacy",
                items = listOf(
                    SettingsItem("Data Export", Icons.Default.Download) {
                        onNavigateToSecondaryScreen(com.Hamode.periodpal.ui.navigation.SecondaryDestination.DATA_EXPORT)
                    },
                    SettingsItem("Privacy Settings", Icons.Default.Security) {
                        onNavigateToSecondaryScreen(com.Hamode.periodpal.ui.navigation.SecondaryDestination.PRIVACY_SETTINGS)
                    },
                    SettingsItem("Emergency Contacts", Icons.Default.ContactPhone) {
                        onNavigateToSecondaryScreen(com.Hamode.periodpal.ui.navigation.SecondaryDestination.EMERGENCY_CONTACTS)
                    }
                )
            )

            SettingsSection(
                title = "Support",
                items = listOf(
                    SettingsItem("Help & FAQ", Icons.Default.Help) {
                        onNavigateToSecondaryScreen(com.Hamode.periodpal.ui.navigation.SecondaryDestination.HELP_FAQ)
                    },
                    SettingsItem("Contact Support", Icons.Default.Support) {
                        onNavigateToSecondaryScreen(com.Hamode.periodpal.ui.navigation.SecondaryDestination.CONTACT_SUPPORT)
                    },
                    SettingsItem("About PeriodPal", Icons.Default.Info) {
                        onNavigateToSecondaryScreen(com.Hamode.periodpal.ui.navigation.SecondaryDestination.ABOUT)
                    }
                )
            )

            // Sign Out Button
            Button(
                onClick = {
                    profileViewModel.signOut()
                    onSignOut()
                },
            modifier = Modifier
                .fillMaxWidth()
                .height(56.dp),
            colors = ButtonDefaults.buttonColors(
                containerColor = ErrorRed
            ),
            shape = RoundedCornerShape(12.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.Logout,
                    contentDescription = "Sign Out",
                    tint = Color.White
                )
                Text(
                    text = "Sign Out",
                    color = Color.White,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium
                )
            }
        }

            // Bottom spacing
            Spacer(modifier = Modifier.height(80.dp))
        }
    }
}

@Composable
private fun ProfileHeader(
    userProfile: UserProfile?,
    onEditProfile: () -> Unit,
    onRefresh: () -> Unit,
    onAvatarSelected: (String) -> Unit,
    isLoading: Boolean,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        shape = RoundedCornerShape(20.dp),
        colors = CardDefaults.cardColors(
            containerColor = RosePink40
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // Profile Avatar
            AvatarSelector(
                currentAvatarId = userProfile?.avatarId ?: "female_1",
                onAvatarSelected = onAvatarSelected
            )

            Spacer(modifier = Modifier.height(16.dp))

            // User Info
            Text(
                text = userProfile?.displayName ?: "User",
                fontSize = 24.sp,
                fontWeight = FontWeight.Bold,
                color = Color.White,
                textAlign = TextAlign.Center
            )
            Text(
                text = userProfile?.email ?: "No email",
                fontSize = 14.sp,
                color = Color.White.copy(alpha = 0.9f),
                textAlign = TextAlign.Center
            )

            Spacer(modifier = Modifier.height(16.dp))

            // Action Buttons Row
            Row(
                horizontalArrangement = Arrangement.spacedBy(12.dp),
                modifier = Modifier.fillMaxWidth()
            ) {
                // Edit Profile Button
                OutlinedButton(
                    onClick = onEditProfile,
                    colors = ButtonDefaults.outlinedButtonColors(
                        contentColor = Color.White
                    ),
                    border = androidx.compose.foundation.BorderStroke(1.dp, Color.White),
                    modifier = Modifier.weight(1f)
                ) {
                    Icon(
                        imageVector = Icons.Default.Edit,
                        contentDescription = "Edit",
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("Edit Profile")
                }

                // Refresh Button
                OutlinedButton(
                    onClick = onRefresh,
                    colors = ButtonDefaults.outlinedButtonColors(
                        contentColor = Color.White
                    ),
                    border = androidx.compose.foundation.BorderStroke(1.dp, Color.White),
                    enabled = !isLoading
                ) {
                    if (isLoading) {
                        CircularProgressIndicator(
                            color = Color.White,
                            modifier = Modifier.size(16.dp)
                        )
                    } else {
                        Icon(
                            imageVector = Icons.Default.Refresh,
                            contentDescription = "Refresh",
                            modifier = Modifier.size(16.dp)
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun QuickStatsSection(
    userProfile: UserProfile?,
    cycleStatistics: com.Hamode.periodpal.data.models.CycleStatistics?,
    nextPeriodDays: Int?,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Quick Stats",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold,
                color = MaterialTheme.colorScheme.onSurface,
                modifier = Modifier.padding(bottom = 16.dp)
            )

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                StatItem(
                    title = "Cycle Length",
                    value = "${userProfile?.averageCycleLength ?: 28} days",
                    icon = Icons.Default.CalendarToday,
                    color = RosePink40
                )
                StatItem(
                    title = "Period Length",
                    value = "${userProfile?.averagePeriodLength ?: 5} days",
                    icon = Icons.Default.Schedule,
                    color = PeriodRed
                )
                StatItem(
                    title = "Next Period",
                    value = when {
                        nextPeriodDays == null -> "Unknown"
                        nextPeriodDays > 0 -> "$nextPeriodDays days"
                        nextPeriodDays == 0 -> "Today"
                        else -> "${-nextPeriodDays} days late"
                    },
                    icon = Icons.Default.Upcoming,
                    color = when {
                        nextPeriodDays == null -> DarkGray
                        nextPeriodDays <= 3 -> PeriodRed
                        nextPeriodDays <= 7 -> RosePink40
                        else -> FertileGreen
                    }
                )
            }
        }
    }
}

@Composable
private fun StatItem(
    title: String,
    value: String,
    icon: ImageVector,
    color: Color,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            imageVector = icon,
            contentDescription = title,
            tint = color,
            modifier = Modifier.size(24.dp)
        )
        Spacer(modifier = Modifier.height(4.dp))
        Text(
            text = value,
            fontSize = 16.sp,
            fontWeight = FontWeight.Bold,
            color = color
        )
        Text(
            text = title,
            fontSize = 12.sp,
            color = DarkGray,
            textAlign = TextAlign.Center
        )
    }
}

@Composable
private fun SettingsSection(
    title: String,
    items: List<SettingsItem>,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold,
                color = MaterialTheme.colorScheme.onSurface,
                modifier = Modifier.padding(bottom = 12.dp)
            )

            items.forEachIndexed { index, item ->
                SettingsItemRow(
                    item = item,
                    showDivider = index < items.size - 1
                )
            }
        }
    }
}

@Composable
private fun SettingsItemRow(
    item: SettingsItem,
    showDivider: Boolean,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .clickable { item.onClick() }
                .padding(vertical = 12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = item.icon,
                contentDescription = item.title,
                tint = RosePink40,
                modifier = Modifier.size(20.dp)
            )
            Spacer(modifier = Modifier.width(16.dp))
            Text(
                text = item.title,
                fontSize = 16.sp,
                color = MaterialTheme.colorScheme.onSurface,
                modifier = Modifier.weight(1f)
            )
            Icon(
                imageVector = Icons.Default.ChevronRight,
                contentDescription = "Navigate",
                tint = DarkGray,
                modifier = Modifier.size(16.dp)
            )
        }

        if (showDivider) {
            Divider(
                color = LightGray,
                thickness = 1.dp,
                modifier = Modifier.padding(start = 36.dp)
            )
        }
    }
}

data class SettingsItem(
    val title: String,
    val icon: ImageVector,
    val onClick: () -> Unit
)
