  Activity android.app  Application android.app  FirebaseApp android.app.Activity  PeriodPalApp android.app.Activity  PeriodPalTheme android.app.Activity  enableEdgeToEdge android.app.Activity  onCreate android.app.Activity  
setContent android.app.Activity  window android.app.Activity  FirebaseApp android.app.Application  onCreate android.app.Application  Context android.content  FirebaseApp android.content.Context  PeriodPalApp android.content.Context  PeriodPalTheme android.content.Context  enableEdgeToEdge android.content.Context  
setContent android.content.Context  FirebaseApp android.content.ContextWrapper  PeriodPalApp android.content.ContextWrapper  PeriodPalTheme android.content.ContextWrapper  enableEdgeToEdge android.content.ContextWrapper  
setContent android.content.ContextWrapper  Build 
android.os  Bundle 
android.os  SDK_INT android.os.Build.VERSION  S android.os.Build.VERSION_CODES  View android.view  FirebaseApp  android.view.ContextThemeWrapper  PeriodPalApp  android.view.ContextThemeWrapper  PeriodPalTheme  android.view.ContextThemeWrapper  enableEdgeToEdge  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  context android.view.View  isInEditMode android.view.View  statusBarColor android.view.Window  ComponentActivity androidx.activity  enableEdgeToEdge androidx.activity  FirebaseApp #androidx.activity.ComponentActivity  PeriodPalApp #androidx.activity.ComponentActivity  PeriodPalTheme #androidx.activity.ComponentActivity  enableEdgeToEdge #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  
setContent androidx.activity.compose  BorderStroke androidx.compose.foundation  ScrollState androidx.compose.foundation  
background androidx.compose.foundation  border androidx.compose.foundation  	clickable androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  rememberScrollState androidx.compose.foundation  verticalScroll androidx.compose.foundation  Add "androidx.compose.foundation.layout  	Alignment "androidx.compose.foundation.layout  	Analytics "androidx.compose.foundation.layout  Arrangement "androidx.compose.foundation.layout  
Assignment "androidx.compose.foundation.layout  
AuthViewModel "androidx.compose.foundation.layout  Badge "androidx.compose.foundation.layout  BarChart "androidx.compose.foundation.layout  Boolean "androidx.compose.foundation.layout  
BottomNavItem "androidx.compose.foundation.layout  Box "androidx.compose.foundation.layout  BoxScope "androidx.compose.foundation.layout  Brush "androidx.compose.foundation.layout  Bundle "androidx.compose.foundation.layout  Button "androidx.compose.foundation.layout  ButtonDefaults "androidx.compose.foundation.layout  CalendarDay "androidx.compose.foundation.layout  CalendarGrid "androidx.compose.foundation.layout  CalendarHeader "androidx.compose.foundation.layout  CalendarLegend "androidx.compose.foundation.layout  
CalendarMonth "androidx.compose.foundation.layout  
CalendarToday "androidx.compose.foundation.layout  Card "androidx.compose.foundation.layout  CardDefaults "androidx.compose.foundation.layout  ChevronLeft "androidx.compose.foundation.layout  ChevronRight "androidx.compose.foundation.layout  Circle "androidx.compose.foundation.layout  CircleShape "androidx.compose.foundation.layout  CircularProgressIndicator "androidx.compose.foundation.layout  Color "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  ComponentActivity "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  ContactPhone "androidx.compose.foundation.layout  CycleOverviewCard "androidx.compose.foundation.layout  
CyclePhase "androidx.compose.foundation.layout  DailyLog "androidx.compose.foundation.layout  DarkGray "androidx.compose.foundation.layout  DateSelectionHeader "androidx.compose.foundation.layout  DateTimeFormatter "androidx.compose.foundation.layout  Divider "androidx.compose.foundation.layout  Download "androidx.compose.foundation.layout  Eco "androidx.compose.foundation.layout  Edit "androidx.compose.foundation.layout  Email "androidx.compose.foundation.layout  ErrorRed "androidx.compose.foundation.layout  Favorite "androidx.compose.foundation.layout  FertileGreen "androidx.compose.foundation.layout  FirebaseApp "androidx.compose.foundation.layout  
FlowIntensity "androidx.compose.foundation.layout  FlowIntensityButton "androidx.compose.foundation.layout  FlowIntensityChip "androidx.compose.foundation.layout  FlowIntensitySection "androidx.compose.foundation.layout  
FontWeight "androidx.compose.foundation.layout  	GridCells "androidx.compose.foundation.layout  
HeaderSection "androidx.compose.foundation.layout  Healing "androidx.compose.foundation.layout  HealthAndSafety "androidx.compose.foundation.layout  HealthInsightSeverity "androidx.compose.foundation.layout  HealthInsightsPreview "androidx.compose.foundation.layout  Help "androidx.compose.foundation.layout  Home "androidx.compose.foundation.layout  Icon "androidx.compose.foundation.layout  
IconButton "androidx.compose.foundation.layout  Icons "androidx.compose.foundation.layout  ImageVector "androidx.compose.foundation.layout  Info "androidx.compose.foundation.layout  InfoBlue "androidx.compose.foundation.layout  InsightCard "androidx.compose.foundation.layout  InsightItem "androidx.compose.foundation.layout  InsightsHeader "androidx.compose.foundation.layout  Int "androidx.compose.foundation.layout  KeyboardOptions "androidx.compose.foundation.layout  KeyboardType "androidx.compose.foundation.layout  LaunchedEffect "androidx.compose.foundation.layout  
LazyColumn "androidx.compose.foundation.layout  LazyVerticalGrid "androidx.compose.foundation.layout  
LegendItem "androidx.compose.foundation.layout  	LightGray "androidx.compose.foundation.layout  	Lightbulb "androidx.compose.foundation.layout  LinearProgressIndicator "androidx.compose.foundation.layout  List "androidx.compose.foundation.layout  	LocalDate "androidx.compose.foundation.layout  Lock "androidx.compose.foundation.layout  LoggingSection "androidx.compose.foundation.layout  Logout "androidx.compose.foundation.layout  Map "androidx.compose.foundation.layout  
MaterialTheme "androidx.compose.foundation.layout  Modifier "androidx.compose.foundation.layout  Mood "androidx.compose.foundation.layout  NavigationDestination "androidx.compose.foundation.layout  Notes "androidx.compose.foundation.layout  
Notifications "androidx.compose.foundation.layout  Opacity "androidx.compose.foundation.layout  OutlinedButton "androidx.compose.foundation.layout  OutlinedTextField "androidx.compose.foundation.layout  OutlinedTextFieldDefaults "androidx.compose.foundation.layout  OverviewItem "androidx.compose.foundation.layout  OverviewTab "androidx.compose.foundation.layout  	PMSPurple "androidx.compose.foundation.layout  
PaddingValues "androidx.compose.foundation.layout  	PainLevel "androidx.compose.foundation.layout  
PainLevelChip "androidx.compose.foundation.layout  PainLevelSection "androidx.compose.foundation.layout  PasswordVisualTransformation "androidx.compose.foundation.layout  PatternsTab "androidx.compose.foundation.layout  PeriodPalApp "androidx.compose.foundation.layout  PeriodPalTheme "androidx.compose.foundation.layout  PeriodPalThemeColors "androidx.compose.foundation.layout  	PeriodRed "androidx.compose.foundation.layout  Person "androidx.compose.foundation.layout  	PlayArrow "androidx.compose.foundation.layout  PredictionsTab "androidx.compose.foundation.layout  Preview "androidx.compose.foundation.layout  
ProfileHeader "androidx.compose.foundation.layout  QuickActionButton "androidx.compose.foundation.layout  QuickActionButtons "androidx.compose.foundation.layout  QuickStatsSection "androidx.compose.foundation.layout  
RosePink40 "androidx.compose.foundation.layout  RoundedCornerShape "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  Save "androidx.compose.foundation.layout  Scaffold "androidx.compose.foundation.layout  Schedule "androidx.compose.foundation.layout  Security "androidx.compose.foundation.layout  SelectedDateInfo "androidx.compose.foundation.layout  Settings "androidx.compose.foundation.layout  SettingsItem "androidx.compose.foundation.layout  SettingsItemRow "androidx.compose.foundation.layout  SettingsSection "androidx.compose.foundation.layout  
SoftPink40 "androidx.compose.foundation.layout  
SoftPink80 "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  StatItem "androidx.compose.foundation.layout  Stop "androidx.compose.foundation.layout  String "androidx.compose.foundation.layout  SuccessGreen "androidx.compose.foundation.layout  Support "androidx.compose.foundation.layout  SymptomPatternItem "androidx.compose.foundation.layout  Tab "androidx.compose.foundation.layout  TabRow "androidx.compose.foundation.layout  Text "androidx.compose.foundation.layout  	TextAlign "androidx.compose.foundation.layout  
TextButton "androidx.compose.foundation.layout  TodayLoggingSection "androidx.compose.foundation.layout  
TrendingUp "androidx.compose.foundation.layout  Unit "androidx.compose.foundation.layout  Upcoming "androidx.compose.foundation.layout  
Visibility "androidx.compose.foundation.layout  
VisibilityOff "androidx.compose.foundation.layout  VisualTransformation "androidx.compose.foundation.layout  
WarningOrange "androidx.compose.foundation.layout  	WaterDrop "androidx.compose.foundation.layout  White "androidx.compose.foundation.layout  	YearMonth "androidx.compose.foundation.layout  align "androidx.compose.foundation.layout  androidx "androidx.compose.foundation.layout  
background "androidx.compose.foundation.layout  buttonColors "androidx.compose.foundation.layout  
cardColors "androidx.compose.foundation.layout  
cardElevation "androidx.compose.foundation.layout  clip "androidx.compose.foundation.layout  collectAsState "androidx.compose.foundation.layout  colors "androidx.compose.foundation.layout  com "androidx.compose.foundation.layout  emptyMap "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  forEach "androidx.compose.foundation.layout  forEachIndexed "androidx.compose.foundation.layout  getFlowColor "androidx.compose.foundation.layout  getPainColor "androidx.compose.foundation.layout  
getPhaseColor "androidx.compose.foundation.layout  getValue "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  ifEmpty "androidx.compose.foundation.layout  
isNotEmpty "androidx.compose.foundation.layout  lightColorScheme "androidx.compose.foundation.layout  listOf "androidx.compose.foundation.layout  mutableStateOf "androidx.compose.foundation.layout  outlinedButtonColors "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  provideDelegate "androidx.compose.foundation.layout  remember "androidx.compose.foundation.layout  rememberCoroutineScope "androidx.compose.foundation.layout  rememberScrollState "androidx.compose.foundation.layout  setValue "androidx.compose.foundation.layout  size "androidx.compose.foundation.layout  spacedBy "androidx.compose.foundation.layout  textButtonColors "androidx.compose.foundation.layout  verticalGradient "androidx.compose.foundation.layout  verticalScroll "androidx.compose.foundation.layout  weight "androidx.compose.foundation.layout  width "androidx.compose.foundation.layout  Center .androidx.compose.foundation.layout.Arrangement  
Horizontal .androidx.compose.foundation.layout.Arrangement  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  SpaceBetween .androidx.compose.foundation.layout.Arrangement  SpaceEvenly .androidx.compose.foundation.layout.Arrangement  Vertical .androidx.compose.foundation.layout.Arrangement  spacedBy .androidx.compose.foundation.layout.Arrangement  	Alignment +androidx.compose.foundation.layout.BoxScope  Arrangement +androidx.compose.foundation.layout.BoxScope  Badge +androidx.compose.foundation.layout.BoxScope  Button +androidx.compose.foundation.layout.BoxScope  ButtonDefaults +androidx.compose.foundation.layout.BoxScope  Card +androidx.compose.foundation.layout.BoxScope  CardDefaults +androidx.compose.foundation.layout.BoxScope  CircularProgressIndicator +androidx.compose.foundation.layout.BoxScope  Color +androidx.compose.foundation.layout.BoxScope  Column +androidx.compose.foundation.layout.BoxScope  
FontWeight +androidx.compose.foundation.layout.BoxScope  Icon +androidx.compose.foundation.layout.BoxScope  Icons +androidx.compose.foundation.layout.BoxScope  
MaterialTheme +androidx.compose.foundation.layout.BoxScope  Modifier +androidx.compose.foundation.layout.BoxScope  Person +androidx.compose.foundation.layout.BoxScope  RoundedCornerShape +androidx.compose.foundation.layout.BoxScope  Spacer +androidx.compose.foundation.layout.BoxScope  Text +androidx.compose.foundation.layout.BoxScope  	TextAlign +androidx.compose.foundation.layout.BoxScope  align +androidx.compose.foundation.layout.BoxScope  androidx +androidx.compose.foundation.layout.BoxScope  buttonColors +androidx.compose.foundation.layout.BoxScope  
cardColors +androidx.compose.foundation.layout.BoxScope  
cardElevation +androidx.compose.foundation.layout.BoxScope  com +androidx.compose.foundation.layout.BoxScope  dp +androidx.compose.foundation.layout.BoxScope  fillMaxWidth +androidx.compose.foundation.layout.BoxScope  height +androidx.compose.foundation.layout.BoxScope  padding +androidx.compose.foundation.layout.BoxScope  size +androidx.compose.foundation.layout.BoxScope  sp +androidx.compose.foundation.layout.BoxScope  spacedBy +androidx.compose.foundation.layout.BoxScope  Add .androidx.compose.foundation.layout.ColumnScope  	Alignment .androidx.compose.foundation.layout.ColumnScope  	Analytics .androidx.compose.foundation.layout.ColumnScope  Arrangement .androidx.compose.foundation.layout.ColumnScope  Badge .androidx.compose.foundation.layout.ColumnScope  
BottomNavItem .androidx.compose.foundation.layout.ColumnScope  Box .androidx.compose.foundation.layout.ColumnScope  Button .androidx.compose.foundation.layout.ColumnScope  ButtonDefaults .androidx.compose.foundation.layout.ColumnScope  CalendarDay .androidx.compose.foundation.layout.ColumnScope  CalendarGrid .androidx.compose.foundation.layout.ColumnScope  CalendarHeader .androidx.compose.foundation.layout.ColumnScope  CalendarLegend .androidx.compose.foundation.layout.ColumnScope  
CalendarToday .androidx.compose.foundation.layout.ColumnScope  Card .androidx.compose.foundation.layout.ColumnScope  CardDefaults .androidx.compose.foundation.layout.ColumnScope  ChevronLeft .androidx.compose.foundation.layout.ColumnScope  ChevronRight .androidx.compose.foundation.layout.ColumnScope  Circle .androidx.compose.foundation.layout.ColumnScope  CircleShape .androidx.compose.foundation.layout.ColumnScope  CircularProgressIndicator .androidx.compose.foundation.layout.ColumnScope  Color .androidx.compose.foundation.layout.ColumnScope  Column .androidx.compose.foundation.layout.ColumnScope  ContactPhone .androidx.compose.foundation.layout.ColumnScope  CycleOverviewCard .androidx.compose.foundation.layout.ColumnScope  
CyclePhase .androidx.compose.foundation.layout.ColumnScope  DarkGray .androidx.compose.foundation.layout.ColumnScope  DateSelectionHeader .androidx.compose.foundation.layout.ColumnScope  DateTimeFormatter .androidx.compose.foundation.layout.ColumnScope  Divider .androidx.compose.foundation.layout.ColumnScope  Download .androidx.compose.foundation.layout.ColumnScope  Edit .androidx.compose.foundation.layout.ColumnScope  Email .androidx.compose.foundation.layout.ColumnScope  ErrorRed .androidx.compose.foundation.layout.ColumnScope  FertileGreen .androidx.compose.foundation.layout.ColumnScope  
FlowIntensity .androidx.compose.foundation.layout.ColumnScope  FlowIntensityButton .androidx.compose.foundation.layout.ColumnScope  FlowIntensitySection .androidx.compose.foundation.layout.ColumnScope  
FontWeight .androidx.compose.foundation.layout.ColumnScope  	GridCells .androidx.compose.foundation.layout.ColumnScope  
HeaderSection .androidx.compose.foundation.layout.ColumnScope  HealthAndSafety .androidx.compose.foundation.layout.ColumnScope  HealthInsightSeverity .androidx.compose.foundation.layout.ColumnScope  HealthInsightsPreview .androidx.compose.foundation.layout.ColumnScope  Help .androidx.compose.foundation.layout.ColumnScope  Icon .androidx.compose.foundation.layout.ColumnScope  
IconButton .androidx.compose.foundation.layout.ColumnScope  Icons .androidx.compose.foundation.layout.ColumnScope  Info .androidx.compose.foundation.layout.ColumnScope  InsightItem .androidx.compose.foundation.layout.ColumnScope  InsightsHeader .androidx.compose.foundation.layout.ColumnScope  KeyboardOptions .androidx.compose.foundation.layout.ColumnScope  KeyboardType .androidx.compose.foundation.layout.ColumnScope  
LazyColumn .androidx.compose.foundation.layout.ColumnScope  LazyVerticalGrid .androidx.compose.foundation.layout.ColumnScope  
LegendItem .androidx.compose.foundation.layout.ColumnScope  	LightGray .androidx.compose.foundation.layout.ColumnScope  LinearProgressIndicator .androidx.compose.foundation.layout.ColumnScope  	LocalDate .androidx.compose.foundation.layout.ColumnScope  Lock .androidx.compose.foundation.layout.ColumnScope  LoggingSection .androidx.compose.foundation.layout.ColumnScope  Logout .androidx.compose.foundation.layout.ColumnScope  
MaterialTheme .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  Mood .androidx.compose.foundation.layout.ColumnScope  NavigationDestination .androidx.compose.foundation.layout.ColumnScope  Notes .androidx.compose.foundation.layout.ColumnScope  
Notifications .androidx.compose.foundation.layout.ColumnScope  OutlinedButton .androidx.compose.foundation.layout.ColumnScope  OutlinedTextField .androidx.compose.foundation.layout.ColumnScope  OutlinedTextFieldDefaults .androidx.compose.foundation.layout.ColumnScope  OverviewItem .androidx.compose.foundation.layout.ColumnScope  OverviewTab .androidx.compose.foundation.layout.ColumnScope  	PMSPurple .androidx.compose.foundation.layout.ColumnScope  PainLevelSection .androidx.compose.foundation.layout.ColumnScope  PasswordVisualTransformation .androidx.compose.foundation.layout.ColumnScope  PatternsTab .androidx.compose.foundation.layout.ColumnScope  PeriodPalThemeColors .androidx.compose.foundation.layout.ColumnScope  	PeriodRed .androidx.compose.foundation.layout.ColumnScope  Person .androidx.compose.foundation.layout.ColumnScope  	PlayArrow .androidx.compose.foundation.layout.ColumnScope  PredictionsTab .androidx.compose.foundation.layout.ColumnScope  
ProfileHeader .androidx.compose.foundation.layout.ColumnScope  QuickActionButton .androidx.compose.foundation.layout.ColumnScope  QuickActionButtons .androidx.compose.foundation.layout.ColumnScope  QuickStatsSection .androidx.compose.foundation.layout.ColumnScope  
RosePink40 .androidx.compose.foundation.layout.ColumnScope  RoundedCornerShape .androidx.compose.foundation.layout.ColumnScope  Row .androidx.compose.foundation.layout.ColumnScope  Save .androidx.compose.foundation.layout.ColumnScope  Schedule .androidx.compose.foundation.layout.ColumnScope  Security .androidx.compose.foundation.layout.ColumnScope  SelectedDateInfo .androidx.compose.foundation.layout.ColumnScope  Settings .androidx.compose.foundation.layout.ColumnScope  SettingsItem .androidx.compose.foundation.layout.ColumnScope  SettingsItemRow .androidx.compose.foundation.layout.ColumnScope  SettingsSection .androidx.compose.foundation.layout.ColumnScope  
SoftPink40 .androidx.compose.foundation.layout.ColumnScope  Spacer .androidx.compose.foundation.layout.ColumnScope  StatItem .androidx.compose.foundation.layout.ColumnScope  Stop .androidx.compose.foundation.layout.ColumnScope  SuccessGreen .androidx.compose.foundation.layout.ColumnScope  Support .androidx.compose.foundation.layout.ColumnScope  SymptomPatternItem .androidx.compose.foundation.layout.ColumnScope  Tab .androidx.compose.foundation.layout.ColumnScope  TabRow .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  	TextAlign .androidx.compose.foundation.layout.ColumnScope  
TextButton .androidx.compose.foundation.layout.ColumnScope  TodayLoggingSection .androidx.compose.foundation.layout.ColumnScope  
TrendingUp .androidx.compose.foundation.layout.ColumnScope  Upcoming .androidx.compose.foundation.layout.ColumnScope  
Visibility .androidx.compose.foundation.layout.ColumnScope  
VisibilityOff .androidx.compose.foundation.layout.ColumnScope  VisualTransformation .androidx.compose.foundation.layout.ColumnScope  White .androidx.compose.foundation.layout.ColumnScope  align .androidx.compose.foundation.layout.ColumnScope  androidx .androidx.compose.foundation.layout.ColumnScope  
background .androidx.compose.foundation.layout.ColumnScope  buttonColors .androidx.compose.foundation.layout.ColumnScope  
cardColors .androidx.compose.foundation.layout.ColumnScope  
cardElevation .androidx.compose.foundation.layout.ColumnScope  clip .androidx.compose.foundation.layout.ColumnScope  colors .androidx.compose.foundation.layout.ColumnScope  com .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  fillMaxSize .androidx.compose.foundation.layout.ColumnScope  fillMaxWidth .androidx.compose.foundation.layout.ColumnScope  forEach .androidx.compose.foundation.layout.ColumnScope  forEachIndexed .androidx.compose.foundation.layout.ColumnScope  
getPhaseColor .androidx.compose.foundation.layout.ColumnScope  height .androidx.compose.foundation.layout.ColumnScope  ifEmpty .androidx.compose.foundation.layout.ColumnScope  
isNotEmpty .androidx.compose.foundation.layout.ColumnScope  listOf .androidx.compose.foundation.layout.ColumnScope  outlinedButtonColors .androidx.compose.foundation.layout.ColumnScope  padding .androidx.compose.foundation.layout.ColumnScope  rememberScrollState .androidx.compose.foundation.layout.ColumnScope  size .androidx.compose.foundation.layout.ColumnScope  sp .androidx.compose.foundation.layout.ColumnScope  spacedBy .androidx.compose.foundation.layout.ColumnScope  textButtonColors .androidx.compose.foundation.layout.ColumnScope  verticalScroll .androidx.compose.foundation.layout.ColumnScope  weight .androidx.compose.foundation.layout.ColumnScope  width .androidx.compose.foundation.layout.ColumnScope  Add +androidx.compose.foundation.layout.RowScope  	Alignment +androidx.compose.foundation.layout.RowScope  	Analytics +androidx.compose.foundation.layout.RowScope  Arrangement +androidx.compose.foundation.layout.RowScope  
BottomNavItem +androidx.compose.foundation.layout.RowScope  Box +androidx.compose.foundation.layout.RowScope  ButtonDefaults +androidx.compose.foundation.layout.RowScope  
CalendarToday +androidx.compose.foundation.layout.RowScope  ChevronLeft +androidx.compose.foundation.layout.RowScope  ChevronRight +androidx.compose.foundation.layout.RowScope  Circle +androidx.compose.foundation.layout.RowScope  CircleShape +androidx.compose.foundation.layout.RowScope  CircularProgressIndicator +androidx.compose.foundation.layout.RowScope  Color +androidx.compose.foundation.layout.RowScope  Column +androidx.compose.foundation.layout.RowScope  DarkGray +androidx.compose.foundation.layout.RowScope  DateTimeFormatter +androidx.compose.foundation.layout.RowScope  Edit +androidx.compose.foundation.layout.RowScope  FertileGreen +androidx.compose.foundation.layout.RowScope  
FlowIntensity +androidx.compose.foundation.layout.RowScope  FlowIntensityButton +androidx.compose.foundation.layout.RowScope  
FontWeight +androidx.compose.foundation.layout.RowScope  Icon +androidx.compose.foundation.layout.RowScope  
IconButton +androidx.compose.foundation.layout.RowScope  Icons +androidx.compose.foundation.layout.RowScope  
LegendItem +androidx.compose.foundation.layout.RowScope  Logout +androidx.compose.foundation.layout.RowScope  
MaterialTheme +androidx.compose.foundation.layout.RowScope  Modifier +androidx.compose.foundation.layout.RowScope  NavigationDestination +androidx.compose.foundation.layout.RowScope  
Notifications +androidx.compose.foundation.layout.RowScope  OverviewItem +androidx.compose.foundation.layout.RowScope  	PMSPurple +androidx.compose.foundation.layout.RowScope  PeriodPalThemeColors +androidx.compose.foundation.layout.RowScope  	PeriodRed +androidx.compose.foundation.layout.RowScope  	PlayArrow +androidx.compose.foundation.layout.RowScope  QuickActionButton +androidx.compose.foundation.layout.RowScope  
RosePink40 +androidx.compose.foundation.layout.RowScope  RoundedCornerShape +androidx.compose.foundation.layout.RowScope  Row +androidx.compose.foundation.layout.RowScope  Save +androidx.compose.foundation.layout.RowScope  Schedule +androidx.compose.foundation.layout.RowScope  Settings +androidx.compose.foundation.layout.RowScope  
SoftPink40 +androidx.compose.foundation.layout.RowScope  Spacer +androidx.compose.foundation.layout.RowScope  StatItem +androidx.compose.foundation.layout.RowScope  Stop +androidx.compose.foundation.layout.RowScope  SuccessGreen +androidx.compose.foundation.layout.RowScope  Text +androidx.compose.foundation.layout.RowScope  	TextAlign +androidx.compose.foundation.layout.RowScope  
TextButton +androidx.compose.foundation.layout.RowScope  
TrendingUp +androidx.compose.foundation.layout.RowScope  Upcoming +androidx.compose.foundation.layout.RowScope  White +androidx.compose.foundation.layout.RowScope  
background +androidx.compose.foundation.layout.RowScope  clip +androidx.compose.foundation.layout.RowScope  dp +androidx.compose.foundation.layout.RowScope  forEach +androidx.compose.foundation.layout.RowScope  
getPhaseColor +androidx.compose.foundation.layout.RowScope  listOf +androidx.compose.foundation.layout.RowScope  padding +androidx.compose.foundation.layout.RowScope  size +androidx.compose.foundation.layout.RowScope  sp +androidx.compose.foundation.layout.RowScope  spacedBy +androidx.compose.foundation.layout.RowScope  textButtonColors +androidx.compose.foundation.layout.RowScope  weight +androidx.compose.foundation.layout.RowScope  width +androidx.compose.foundation.layout.RowScope  compose +androidx.compose.foundation.layout.androidx  ui 3androidx.compose.foundation.layout.androidx.compose  graphics 6androidx.compose.foundation.layout.androidx.compose.ui  Color ?androidx.compose.foundation.layout.androidx.compose.ui.graphics  Hamode &androidx.compose.foundation.layout.com  	periodpal -androidx.compose.foundation.layout.com.Hamode  ui 7androidx.compose.foundation.layout.com.Hamode.periodpal  	viewmodel :androidx.compose.foundation.layout.com.Hamode.periodpal.ui  	AuthState Dandroidx.compose.foundation.layout.com.Hamode.periodpal.ui.viewmodel  
Authenticated Nandroidx.compose.foundation.layout.com.Hamode.periodpal.ui.viewmodel.AuthState  Loading Nandroidx.compose.foundation.layout.com.Hamode.periodpal.ui.viewmodel.AuthState  Unauthenticated Nandroidx.compose.foundation.layout.com.Hamode.periodpal.ui.viewmodel.AuthState  
LazyColumn  androidx.compose.foundation.lazy  
LazyItemScope  androidx.compose.foundation.lazy  
LazyListScope  androidx.compose.foundation.lazy  LazyRow  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  	Alignment .androidx.compose.foundation.lazy.LazyItemScope  Arrangement .androidx.compose.foundation.lazy.LazyItemScope  BarChart .androidx.compose.foundation.lazy.LazyItemScope  Button .androidx.compose.foundation.lazy.LazyItemScope  ButtonDefaults .androidx.compose.foundation.lazy.LazyItemScope  
CalendarToday .androidx.compose.foundation.lazy.LazyItemScope  CircularProgressIndicator .androidx.compose.foundation.lazy.LazyItemScope  Color .androidx.compose.foundation.lazy.LazyItemScope  Column .androidx.compose.foundation.lazy.LazyItemScope  DarkGray .androidx.compose.foundation.lazy.LazyItemScope  DateTimeFormatter .androidx.compose.foundation.lazy.LazyItemScope  Eco .androidx.compose.foundation.lazy.LazyItemScope  Favorite .androidx.compose.foundation.lazy.LazyItemScope  FertileGreen .androidx.compose.foundation.lazy.LazyItemScope  FlowIntensityChip .androidx.compose.foundation.lazy.LazyItemScope  FlowIntensitySection .androidx.compose.foundation.lazy.LazyItemScope  
FontWeight .androidx.compose.foundation.lazy.LazyItemScope  HealthAndSafety .androidx.compose.foundation.lazy.LazyItemScope  HealthInsightSeverity .androidx.compose.foundation.lazy.LazyItemScope  Icon .androidx.compose.foundation.lazy.LazyItemScope  Icons .androidx.compose.foundation.lazy.LazyItemScope  InfoBlue .androidx.compose.foundation.lazy.LazyItemScope  InsightCard .androidx.compose.foundation.lazy.LazyItemScope  InsightItem .androidx.compose.foundation.lazy.LazyItemScope  	Lightbulb .androidx.compose.foundation.lazy.LazyItemScope  	LocalDate .androidx.compose.foundation.lazy.LazyItemScope  LoggingSection .androidx.compose.foundation.lazy.LazyItemScope  Modifier .androidx.compose.foundation.lazy.LazyItemScope  Mood .androidx.compose.foundation.lazy.LazyItemScope  Notes .androidx.compose.foundation.lazy.LazyItemScope  OutlinedTextField .androidx.compose.foundation.lazy.LazyItemScope  OutlinedTextFieldDefaults .androidx.compose.foundation.lazy.LazyItemScope  
PainLevelChip .androidx.compose.foundation.lazy.LazyItemScope  PainLevelSection .androidx.compose.foundation.lazy.LazyItemScope  	PeriodRed .androidx.compose.foundation.lazy.LazyItemScope  
RosePink40 .androidx.compose.foundation.lazy.LazyItemScope  RoundedCornerShape .androidx.compose.foundation.lazy.LazyItemScope  Row .androidx.compose.foundation.lazy.LazyItemScope  Save .androidx.compose.foundation.lazy.LazyItemScope  Spacer .androidx.compose.foundation.lazy.LazyItemScope  SuccessGreen .androidx.compose.foundation.lazy.LazyItemScope  SymptomPatternItem .androidx.compose.foundation.lazy.LazyItemScope  Text .androidx.compose.foundation.lazy.LazyItemScope  
TrendingUp .androidx.compose.foundation.lazy.LazyItemScope  
WarningOrange .androidx.compose.foundation.lazy.LazyItemScope  	WaterDrop .androidx.compose.foundation.lazy.LazyItemScope  buttonColors .androidx.compose.foundation.lazy.LazyItemScope  colors .androidx.compose.foundation.lazy.LazyItemScope  dp .androidx.compose.foundation.lazy.LazyItemScope  fillMaxWidth .androidx.compose.foundation.lazy.LazyItemScope  height .androidx.compose.foundation.lazy.LazyItemScope  padding .androidx.compose.foundation.lazy.LazyItemScope  size .androidx.compose.foundation.lazy.LazyItemScope  sp .androidx.compose.foundation.lazy.LazyItemScope  spacedBy .androidx.compose.foundation.lazy.LazyItemScope  weight .androidx.compose.foundation.lazy.LazyItemScope  	Alignment .androidx.compose.foundation.lazy.LazyListScope  Arrangement .androidx.compose.foundation.lazy.LazyListScope  BarChart .androidx.compose.foundation.lazy.LazyListScope  Button .androidx.compose.foundation.lazy.LazyListScope  ButtonDefaults .androidx.compose.foundation.lazy.LazyListScope  
CalendarToday .androidx.compose.foundation.lazy.LazyListScope  CircularProgressIndicator .androidx.compose.foundation.lazy.LazyListScope  Color .androidx.compose.foundation.lazy.LazyListScope  Column .androidx.compose.foundation.lazy.LazyListScope  DarkGray .androidx.compose.foundation.lazy.LazyListScope  DateTimeFormatter .androidx.compose.foundation.lazy.LazyListScope  Eco .androidx.compose.foundation.lazy.LazyListScope  Favorite .androidx.compose.foundation.lazy.LazyListScope  FertileGreen .androidx.compose.foundation.lazy.LazyListScope  
FlowIntensity .androidx.compose.foundation.lazy.LazyListScope  FlowIntensityChip .androidx.compose.foundation.lazy.LazyListScope  FlowIntensitySection .androidx.compose.foundation.lazy.LazyListScope  
FontWeight .androidx.compose.foundation.lazy.LazyListScope  HealthAndSafety .androidx.compose.foundation.lazy.LazyListScope  HealthInsightSeverity .androidx.compose.foundation.lazy.LazyListScope  Icon .androidx.compose.foundation.lazy.LazyListScope  Icons .androidx.compose.foundation.lazy.LazyListScope  InfoBlue .androidx.compose.foundation.lazy.LazyListScope  InsightCard .androidx.compose.foundation.lazy.LazyListScope  InsightItem .androidx.compose.foundation.lazy.LazyListScope  	Lightbulb .androidx.compose.foundation.lazy.LazyListScope  	LocalDate .androidx.compose.foundation.lazy.LazyListScope  LoggingSection .androidx.compose.foundation.lazy.LazyListScope  Modifier .androidx.compose.foundation.lazy.LazyListScope  Mood .androidx.compose.foundation.lazy.LazyListScope  Notes .androidx.compose.foundation.lazy.LazyListScope  OutlinedTextField .androidx.compose.foundation.lazy.LazyListScope  OutlinedTextFieldDefaults .androidx.compose.foundation.lazy.LazyListScope  	PainLevel .androidx.compose.foundation.lazy.LazyListScope  
PainLevelChip .androidx.compose.foundation.lazy.LazyListScope  PainLevelSection .androidx.compose.foundation.lazy.LazyListScope  	PeriodRed .androidx.compose.foundation.lazy.LazyListScope  
RosePink40 .androidx.compose.foundation.lazy.LazyListScope  RoundedCornerShape .androidx.compose.foundation.lazy.LazyListScope  Row .androidx.compose.foundation.lazy.LazyListScope  Save .androidx.compose.foundation.lazy.LazyListScope  Spacer .androidx.compose.foundation.lazy.LazyListScope  SuccessGreen .androidx.compose.foundation.lazy.LazyListScope  SymptomPatternItem .androidx.compose.foundation.lazy.LazyListScope  Text .androidx.compose.foundation.lazy.LazyListScope  
TrendingUp .androidx.compose.foundation.lazy.LazyListScope  
WarningOrange .androidx.compose.foundation.lazy.LazyListScope  	WaterDrop .androidx.compose.foundation.lazy.LazyListScope  buttonColors .androidx.compose.foundation.lazy.LazyListScope  colors .androidx.compose.foundation.lazy.LazyListScope  dp .androidx.compose.foundation.lazy.LazyListScope  fillMaxWidth .androidx.compose.foundation.lazy.LazyListScope  height .androidx.compose.foundation.lazy.LazyListScope  item .androidx.compose.foundation.lazy.LazyListScope  items .androidx.compose.foundation.lazy.LazyListScope  padding .androidx.compose.foundation.lazy.LazyListScope  size .androidx.compose.foundation.lazy.LazyListScope  sp .androidx.compose.foundation.lazy.LazyListScope  spacedBy .androidx.compose.foundation.lazy.LazyListScope  weight .androidx.compose.foundation.lazy.LazyListScope  	GridCells %androidx.compose.foundation.lazy.grid  LazyGridItemScope %androidx.compose.foundation.lazy.grid  
LazyGridScope %androidx.compose.foundation.lazy.grid  LazyVerticalGrid %androidx.compose.foundation.lazy.grid  items %androidx.compose.foundation.lazy.grid  Fixed /androidx.compose.foundation.lazy.grid.GridCells  Box 7androidx.compose.foundation.lazy.grid.LazyGridItemScope  CalendarDay 7androidx.compose.foundation.lazy.grid.LazyGridItemScope  	LocalDate 7androidx.compose.foundation.lazy.grid.LazyGridItemScope  Modifier 7androidx.compose.foundation.lazy.grid.LazyGridItemScope  dp 7androidx.compose.foundation.lazy.grid.LazyGridItemScope  size 7androidx.compose.foundation.lazy.grid.LazyGridItemScope  Box 3androidx.compose.foundation.lazy.grid.LazyGridScope  CalendarDay 3androidx.compose.foundation.lazy.grid.LazyGridScope  	LocalDate 3androidx.compose.foundation.lazy.grid.LazyGridScope  Modifier 3androidx.compose.foundation.lazy.grid.LazyGridScope  dp 3androidx.compose.foundation.lazy.grid.LazyGridScope  items 3androidx.compose.foundation.lazy.grid.LazyGridScope  size 3androidx.compose.foundation.lazy.grid.LazyGridScope  CircleShape !androidx.compose.foundation.shape  RoundedCornerShape !androidx.compose.foundation.shape  KeyboardOptions  androidx.compose.foundation.text  Icons androidx.compose.material.icons  Default %androidx.compose.material.icons.Icons  Filled %androidx.compose.material.icons.Icons  Add ,androidx.compose.material.icons.Icons.Filled  	Analytics ,androidx.compose.material.icons.Icons.Filled  
Assignment ,androidx.compose.material.icons.Icons.Filled  BarChart ,androidx.compose.material.icons.Icons.Filled  
CalendarMonth ,androidx.compose.material.icons.Icons.Filled  
CalendarToday ,androidx.compose.material.icons.Icons.Filled  ChevronLeft ,androidx.compose.material.icons.Icons.Filled  ChevronRight ,androidx.compose.material.icons.Icons.Filled  Circle ,androidx.compose.material.icons.Icons.Filled  ContactPhone ,androidx.compose.material.icons.Icons.Filled  Download ,androidx.compose.material.icons.Icons.Filled  Eco ,androidx.compose.material.icons.Icons.Filled  Edit ,androidx.compose.material.icons.Icons.Filled  Email ,androidx.compose.material.icons.Icons.Filled  Favorite ,androidx.compose.material.icons.Icons.Filled  Healing ,androidx.compose.material.icons.Icons.Filled  HealthAndSafety ,androidx.compose.material.icons.Icons.Filled  Help ,androidx.compose.material.icons.Icons.Filled  Home ,androidx.compose.material.icons.Icons.Filled  Info ,androidx.compose.material.icons.Icons.Filled  	Lightbulb ,androidx.compose.material.icons.Icons.Filled  Lock ,androidx.compose.material.icons.Icons.Filled  Logout ,androidx.compose.material.icons.Icons.Filled  Mood ,androidx.compose.material.icons.Icons.Filled  Notes ,androidx.compose.material.icons.Icons.Filled  
Notifications ,androidx.compose.material.icons.Icons.Filled  Opacity ,androidx.compose.material.icons.Icons.Filled  Person ,androidx.compose.material.icons.Icons.Filled  	PlayArrow ,androidx.compose.material.icons.Icons.Filled  Save ,androidx.compose.material.icons.Icons.Filled  Schedule ,androidx.compose.material.icons.Icons.Filled  Security ,androidx.compose.material.icons.Icons.Filled  Settings ,androidx.compose.material.icons.Icons.Filled  Stop ,androidx.compose.material.icons.Icons.Filled  Support ,androidx.compose.material.icons.Icons.Filled  
TrendingUp ,androidx.compose.material.icons.Icons.Filled  Upcoming ,androidx.compose.material.icons.Icons.Filled  
Visibility ,androidx.compose.material.icons.Icons.Filled  
VisibilityOff ,androidx.compose.material.icons.Icons.Filled  	WaterDrop ,androidx.compose.material.icons.Icons.Filled  Add &androidx.compose.material.icons.filled  	Alignment &androidx.compose.material.icons.filled  	Analytics &androidx.compose.material.icons.filled  Arrangement &androidx.compose.material.icons.filled  
Assignment &androidx.compose.material.icons.filled  
AuthViewModel &androidx.compose.material.icons.filled  Badge &androidx.compose.material.icons.filled  BarChart &androidx.compose.material.icons.filled  Boolean &androidx.compose.material.icons.filled  
BottomNavItem &androidx.compose.material.icons.filled  Box &androidx.compose.material.icons.filled  Brush &androidx.compose.material.icons.filled  Button &androidx.compose.material.icons.filled  ButtonDefaults &androidx.compose.material.icons.filled  CalendarDay &androidx.compose.material.icons.filled  CalendarGrid &androidx.compose.material.icons.filled  CalendarHeader &androidx.compose.material.icons.filled  CalendarLegend &androidx.compose.material.icons.filled  
CalendarMonth &androidx.compose.material.icons.filled  
CalendarToday &androidx.compose.material.icons.filled  Card &androidx.compose.material.icons.filled  CardDefaults &androidx.compose.material.icons.filled  ChevronLeft &androidx.compose.material.icons.filled  ChevronRight &androidx.compose.material.icons.filled  Circle &androidx.compose.material.icons.filled  CircleShape &androidx.compose.material.icons.filled  CircularProgressIndicator &androidx.compose.material.icons.filled  Color &androidx.compose.material.icons.filled  Column &androidx.compose.material.icons.filled  
Composable &androidx.compose.material.icons.filled  ContactPhone &androidx.compose.material.icons.filled  CycleOverviewCard &androidx.compose.material.icons.filled  
CyclePhase &androidx.compose.material.icons.filled  DailyLog &androidx.compose.material.icons.filled  DarkGray &androidx.compose.material.icons.filled  DateSelectionHeader &androidx.compose.material.icons.filled  DateTimeFormatter &androidx.compose.material.icons.filled  Divider &androidx.compose.material.icons.filled  Download &androidx.compose.material.icons.filled  Eco &androidx.compose.material.icons.filled  Edit &androidx.compose.material.icons.filled  Email &androidx.compose.material.icons.filled  ErrorRed &androidx.compose.material.icons.filled  Favorite &androidx.compose.material.icons.filled  FertileGreen &androidx.compose.material.icons.filled  
FlowIntensity &androidx.compose.material.icons.filled  FlowIntensityButton &androidx.compose.material.icons.filled  FlowIntensityChip &androidx.compose.material.icons.filled  FlowIntensitySection &androidx.compose.material.icons.filled  
FontWeight &androidx.compose.material.icons.filled  	GridCells &androidx.compose.material.icons.filled  
HeaderSection &androidx.compose.material.icons.filled  Healing &androidx.compose.material.icons.filled  HealthAndSafety &androidx.compose.material.icons.filled  HealthInsightSeverity &androidx.compose.material.icons.filled  HealthInsightsPreview &androidx.compose.material.icons.filled  Help &androidx.compose.material.icons.filled  Home &androidx.compose.material.icons.filled  Icon &androidx.compose.material.icons.filled  
IconButton &androidx.compose.material.icons.filled  Icons &androidx.compose.material.icons.filled  ImageVector &androidx.compose.material.icons.filled  Info &androidx.compose.material.icons.filled  InfoBlue &androidx.compose.material.icons.filled  InsightCard &androidx.compose.material.icons.filled  InsightItem &androidx.compose.material.icons.filled  InsightsHeader &androidx.compose.material.icons.filled  Int &androidx.compose.material.icons.filled  KeyboardOptions &androidx.compose.material.icons.filled  KeyboardType &androidx.compose.material.icons.filled  LaunchedEffect &androidx.compose.material.icons.filled  
LazyColumn &androidx.compose.material.icons.filled  LazyVerticalGrid &androidx.compose.material.icons.filled  
LegendItem &androidx.compose.material.icons.filled  	LightGray &androidx.compose.material.icons.filled  	Lightbulb &androidx.compose.material.icons.filled  LinearProgressIndicator &androidx.compose.material.icons.filled  List &androidx.compose.material.icons.filled  	LocalDate &androidx.compose.material.icons.filled  Lock &androidx.compose.material.icons.filled  LoggingSection &androidx.compose.material.icons.filled  Logout &androidx.compose.material.icons.filled  Map &androidx.compose.material.icons.filled  
MaterialTheme &androidx.compose.material.icons.filled  Modifier &androidx.compose.material.icons.filled  Mood &androidx.compose.material.icons.filled  NavigationDestination &androidx.compose.material.icons.filled  Notes &androidx.compose.material.icons.filled  
Notifications &androidx.compose.material.icons.filled  Opacity &androidx.compose.material.icons.filled  OutlinedButton &androidx.compose.material.icons.filled  OutlinedTextField &androidx.compose.material.icons.filled  OutlinedTextFieldDefaults &androidx.compose.material.icons.filled  OverviewItem &androidx.compose.material.icons.filled  OverviewTab &androidx.compose.material.icons.filled  	PMSPurple &androidx.compose.material.icons.filled  	PainLevel &androidx.compose.material.icons.filled  
PainLevelChip &androidx.compose.material.icons.filled  PainLevelSection &androidx.compose.material.icons.filled  PasswordVisualTransformation &androidx.compose.material.icons.filled  PatternsTab &androidx.compose.material.icons.filled  PeriodPalThemeColors &androidx.compose.material.icons.filled  	PeriodRed &androidx.compose.material.icons.filled  Person &androidx.compose.material.icons.filled  	PlayArrow &androidx.compose.material.icons.filled  PredictionsTab &androidx.compose.material.icons.filled  
ProfileHeader &androidx.compose.material.icons.filled  QuickActionButton &androidx.compose.material.icons.filled  QuickActionButtons &androidx.compose.material.icons.filled  QuickStatsSection &androidx.compose.material.icons.filled  
RosePink40 &androidx.compose.material.icons.filled  RoundedCornerShape &androidx.compose.material.icons.filled  Row &androidx.compose.material.icons.filled  Save &androidx.compose.material.icons.filled  Schedule &androidx.compose.material.icons.filled  Security &androidx.compose.material.icons.filled  SelectedDateInfo &androidx.compose.material.icons.filled  Settings &androidx.compose.material.icons.filled  SettingsItem &androidx.compose.material.icons.filled  SettingsItemRow &androidx.compose.material.icons.filled  SettingsSection &androidx.compose.material.icons.filled  
SoftPink40 &androidx.compose.material.icons.filled  
SoftPink80 &androidx.compose.material.icons.filled  Spacer &androidx.compose.material.icons.filled  StatItem &androidx.compose.material.icons.filled  Stop &androidx.compose.material.icons.filled  String &androidx.compose.material.icons.filled  SuccessGreen &androidx.compose.material.icons.filled  Support &androidx.compose.material.icons.filled  SymptomPatternItem &androidx.compose.material.icons.filled  Tab &androidx.compose.material.icons.filled  TabRow &androidx.compose.material.icons.filled  Text &androidx.compose.material.icons.filled  	TextAlign &androidx.compose.material.icons.filled  
TextButton &androidx.compose.material.icons.filled  TodayLoggingSection &androidx.compose.material.icons.filled  
TrendingUp &androidx.compose.material.icons.filled  Unit &androidx.compose.material.icons.filled  Upcoming &androidx.compose.material.icons.filled  
Visibility &androidx.compose.material.icons.filled  
VisibilityOff &androidx.compose.material.icons.filled  VisualTransformation &androidx.compose.material.icons.filled  
WarningOrange &androidx.compose.material.icons.filled  	WaterDrop &androidx.compose.material.icons.filled  White &androidx.compose.material.icons.filled  	YearMonth &androidx.compose.material.icons.filled  align &androidx.compose.material.icons.filled  androidx &androidx.compose.material.icons.filled  
background &androidx.compose.material.icons.filled  buttonColors &androidx.compose.material.icons.filled  
cardColors &androidx.compose.material.icons.filled  
cardElevation &androidx.compose.material.icons.filled  clip &androidx.compose.material.icons.filled  collectAsState &androidx.compose.material.icons.filled  colors &androidx.compose.material.icons.filled  com &androidx.compose.material.icons.filled  emptyMap &androidx.compose.material.icons.filled  fillMaxSize &androidx.compose.material.icons.filled  fillMaxWidth &androidx.compose.material.icons.filled  forEach &androidx.compose.material.icons.filled  forEachIndexed &androidx.compose.material.icons.filled  getFlowColor &androidx.compose.material.icons.filled  getPainColor &androidx.compose.material.icons.filled  
getPhaseColor &androidx.compose.material.icons.filled  getValue &androidx.compose.material.icons.filled  height &androidx.compose.material.icons.filled  ifEmpty &androidx.compose.material.icons.filled  
isNotEmpty &androidx.compose.material.icons.filled  listOf &androidx.compose.material.icons.filled  mutableStateOf &androidx.compose.material.icons.filled  outlinedButtonColors &androidx.compose.material.icons.filled  padding &androidx.compose.material.icons.filled  provideDelegate &androidx.compose.material.icons.filled  remember &androidx.compose.material.icons.filled  rememberCoroutineScope &androidx.compose.material.icons.filled  rememberScrollState &androidx.compose.material.icons.filled  setValue &androidx.compose.material.icons.filled  size &androidx.compose.material.icons.filled  spacedBy &androidx.compose.material.icons.filled  textButtonColors &androidx.compose.material.icons.filled  verticalGradient &androidx.compose.material.icons.filled  verticalScroll &androidx.compose.material.icons.filled  weight &androidx.compose.material.icons.filled  width &androidx.compose.material.icons.filled  compose /androidx.compose.material.icons.filled.androidx  ui 7androidx.compose.material.icons.filled.androidx.compose  graphics :androidx.compose.material.icons.filled.androidx.compose.ui  Color Candroidx.compose.material.icons.filled.androidx.compose.ui.graphics  Hamode *androidx.compose.material.icons.filled.com  	periodpal 1androidx.compose.material.icons.filled.com.Hamode  ui ;androidx.compose.material.icons.filled.com.Hamode.periodpal  	viewmodel >androidx.compose.material.icons.filled.com.Hamode.periodpal.ui  	AuthState Handroidx.compose.material.icons.filled.com.Hamode.periodpal.ui.viewmodel  
Authenticated Randroidx.compose.material.icons.filled.com.Hamode.periodpal.ui.viewmodel.AuthState  Loading Randroidx.compose.material.icons.filled.com.Hamode.periodpal.ui.viewmodel.AuthState  Unauthenticated Randroidx.compose.material.icons.filled.com.Hamode.periodpal.ui.viewmodel.AuthState  Add androidx.compose.material3  	Alignment androidx.compose.material3  	Analytics androidx.compose.material3  Arrangement androidx.compose.material3  
Assignment androidx.compose.material3  
AuthViewModel androidx.compose.material3  Badge androidx.compose.material3  BarChart androidx.compose.material3  Boolean androidx.compose.material3  
BottomNavItem androidx.compose.material3  Box androidx.compose.material3  Brush androidx.compose.material3  Bundle androidx.compose.material3  Button androidx.compose.material3  ButtonColors androidx.compose.material3  ButtonDefaults androidx.compose.material3  CalendarDay androidx.compose.material3  CalendarGrid androidx.compose.material3  CalendarHeader androidx.compose.material3  CalendarLegend androidx.compose.material3  
CalendarMonth androidx.compose.material3  
CalendarToday androidx.compose.material3  Card androidx.compose.material3  
CardColors androidx.compose.material3  CardDefaults androidx.compose.material3  
CardElevation androidx.compose.material3  ChevronLeft androidx.compose.material3  ChevronRight androidx.compose.material3  Circle androidx.compose.material3  CircleShape androidx.compose.material3  CircularProgressIndicator androidx.compose.material3  Color androidx.compose.material3  ColorScheme androidx.compose.material3  Column androidx.compose.material3  ComponentActivity androidx.compose.material3  
Composable androidx.compose.material3  ContactPhone androidx.compose.material3  CycleOverviewCard androidx.compose.material3  
CyclePhase androidx.compose.material3  DailyLog androidx.compose.material3  DarkGray androidx.compose.material3  DateSelectionHeader androidx.compose.material3  DateTimeFormatter androidx.compose.material3  Divider androidx.compose.material3  Download androidx.compose.material3  Eco androidx.compose.material3  Edit androidx.compose.material3  Email androidx.compose.material3  ErrorRed androidx.compose.material3  Favorite androidx.compose.material3  FertileGreen androidx.compose.material3  FirebaseApp androidx.compose.material3  
FlowIntensity androidx.compose.material3  FlowIntensityButton androidx.compose.material3  FlowIntensityChip androidx.compose.material3  FlowIntensitySection androidx.compose.material3  
FontWeight androidx.compose.material3  	GridCells androidx.compose.material3  
HeaderSection androidx.compose.material3  Healing androidx.compose.material3  HealthAndSafety androidx.compose.material3  HealthInsightSeverity androidx.compose.material3  HealthInsightsPreview androidx.compose.material3  Help androidx.compose.material3  Home androidx.compose.material3  Icon androidx.compose.material3  
IconButton androidx.compose.material3  Icons androidx.compose.material3  ImageVector androidx.compose.material3  Info androidx.compose.material3  InfoBlue androidx.compose.material3  InsightCard androidx.compose.material3  InsightItem androidx.compose.material3  InsightsHeader androidx.compose.material3  Int androidx.compose.material3  KeyboardOptions androidx.compose.material3  KeyboardType androidx.compose.material3  LaunchedEffect androidx.compose.material3  
LazyColumn androidx.compose.material3  LazyVerticalGrid androidx.compose.material3  
LegendItem androidx.compose.material3  	LightGray androidx.compose.material3  	Lightbulb androidx.compose.material3  LinearProgressIndicator androidx.compose.material3  List androidx.compose.material3  	LocalDate androidx.compose.material3  Lock androidx.compose.material3  LoggingSection androidx.compose.material3  Logout androidx.compose.material3  Map androidx.compose.material3  
MaterialTheme androidx.compose.material3  Modifier androidx.compose.material3  Mood androidx.compose.material3  NavigationDestination androidx.compose.material3  Notes androidx.compose.material3  
Notifications androidx.compose.material3  Opacity androidx.compose.material3  OutlinedButton androidx.compose.material3  OutlinedTextField androidx.compose.material3  OutlinedTextFieldDefaults androidx.compose.material3  OverviewItem androidx.compose.material3  OverviewTab androidx.compose.material3  	PMSPurple androidx.compose.material3  	PainLevel androidx.compose.material3  
PainLevelChip androidx.compose.material3  PainLevelSection androidx.compose.material3  PasswordVisualTransformation androidx.compose.material3  PatternsTab androidx.compose.material3  PeriodPalApp androidx.compose.material3  PeriodPalTheme androidx.compose.material3  PeriodPalThemeColors androidx.compose.material3  	PeriodRed androidx.compose.material3  Person androidx.compose.material3  	PlayArrow androidx.compose.material3  PredictionsTab androidx.compose.material3  Preview androidx.compose.material3  
ProfileHeader androidx.compose.material3  QuickActionButton androidx.compose.material3  QuickActionButtons androidx.compose.material3  QuickStatsSection androidx.compose.material3  
RosePink40 androidx.compose.material3  RoundedCornerShape androidx.compose.material3  Row androidx.compose.material3  Save androidx.compose.material3  Scaffold androidx.compose.material3  Schedule androidx.compose.material3  Security androidx.compose.material3  SelectedDateInfo androidx.compose.material3  Settings androidx.compose.material3  SettingsItem androidx.compose.material3  SettingsItemRow androidx.compose.material3  SettingsSection androidx.compose.material3  
SoftPink40 androidx.compose.material3  
SoftPink80 androidx.compose.material3  Spacer androidx.compose.material3  StatItem androidx.compose.material3  Stop androidx.compose.material3  String androidx.compose.material3  SuccessGreen androidx.compose.material3  Support androidx.compose.material3  SymptomPatternItem androidx.compose.material3  Tab androidx.compose.material3  TabRow androidx.compose.material3  Text androidx.compose.material3  	TextAlign androidx.compose.material3  
TextButton androidx.compose.material3  TextFieldColors androidx.compose.material3  TodayLoggingSection androidx.compose.material3  
TrendingUp androidx.compose.material3  
Typography androidx.compose.material3  Unit androidx.compose.material3  Upcoming androidx.compose.material3  
Visibility androidx.compose.material3  
VisibilityOff androidx.compose.material3  VisualTransformation androidx.compose.material3  
WarningOrange androidx.compose.material3  	WaterDrop androidx.compose.material3  White androidx.compose.material3  	YearMonth androidx.compose.material3  align androidx.compose.material3  androidx androidx.compose.material3  
background androidx.compose.material3  buttonColors androidx.compose.material3  
cardColors androidx.compose.material3  
cardElevation androidx.compose.material3  clip androidx.compose.material3  collectAsState androidx.compose.material3  colors androidx.compose.material3  com androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  emptyMap androidx.compose.material3  fillMaxSize androidx.compose.material3  fillMaxWidth androidx.compose.material3  forEach androidx.compose.material3  forEachIndexed androidx.compose.material3  getFlowColor androidx.compose.material3  getPainColor androidx.compose.material3  
getPhaseColor androidx.compose.material3  getValue androidx.compose.material3  height androidx.compose.material3  ifEmpty androidx.compose.material3  
isNotEmpty androidx.compose.material3  lightColorScheme androidx.compose.material3  listOf androidx.compose.material3  mutableStateOf androidx.compose.material3  outlinedButtonColors androidx.compose.material3  padding androidx.compose.material3  provideDelegate androidx.compose.material3  remember androidx.compose.material3  rememberCoroutineScope androidx.compose.material3  rememberScrollState androidx.compose.material3  setValue androidx.compose.material3  size androidx.compose.material3  spacedBy androidx.compose.material3  textButtonColors androidx.compose.material3  verticalGradient androidx.compose.material3  verticalScroll androidx.compose.material3  weight androidx.compose.material3  width androidx.compose.material3  buttonColors )androidx.compose.material3.ButtonDefaults  outlinedButtonColors )androidx.compose.material3.ButtonDefaults  textButtonColors )androidx.compose.material3.ButtonDefaults  
cardColors 'androidx.compose.material3.CardDefaults  
cardElevation 'androidx.compose.material3.CardDefaults  
background &androidx.compose.material3.ColorScheme  	onSurface &androidx.compose.material3.ColorScheme  primary &androidx.compose.material3.ColorScheme  surface &androidx.compose.material3.ColorScheme  colorScheme (androidx.compose.material3.MaterialTheme  
typography (androidx.compose.material3.MaterialTheme  colors 4androidx.compose.material3.OutlinedTextFieldDefaults  	bodyLarge %androidx.compose.material3.Typography  
bodyMedium %androidx.compose.material3.Typography  headlineMedium %androidx.compose.material3.Typography  titleMedium %androidx.compose.material3.Typography  compose #androidx.compose.material3.androidx  ui +androidx.compose.material3.androidx.compose  graphics .androidx.compose.material3.androidx.compose.ui  Color 7androidx.compose.material3.androidx.compose.ui.graphics  Hamode androidx.compose.material3.com  	periodpal %androidx.compose.material3.com.Hamode  ui /androidx.compose.material3.com.Hamode.periodpal  	viewmodel 2androidx.compose.material3.com.Hamode.periodpal.ui  	AuthState <androidx.compose.material3.com.Hamode.periodpal.ui.viewmodel  
Authenticated Fandroidx.compose.material3.com.Hamode.periodpal.ui.viewmodel.AuthState  Loading Fandroidx.compose.material3.com.Hamode.periodpal.ui.viewmodel.AuthState  Unauthenticated Fandroidx.compose.material3.com.Hamode.periodpal.ui.viewmodel.AuthState  Add androidx.compose.runtime  	Alignment androidx.compose.runtime  	Analytics androidx.compose.runtime  Arrangement androidx.compose.runtime  
AuthViewModel androidx.compose.runtime  BarChart androidx.compose.runtime  Boolean androidx.compose.runtime  Box androidx.compose.runtime  Brush androidx.compose.runtime  Bundle androidx.compose.runtime  Button androidx.compose.runtime  ButtonDefaults androidx.compose.runtime  CalendarDay androidx.compose.runtime  CalendarGrid androidx.compose.runtime  CalendarHeader androidx.compose.runtime  CalendarLegend androidx.compose.runtime  
CalendarToday androidx.compose.runtime  Card androidx.compose.runtime  CardDefaults androidx.compose.runtime  ChevronLeft androidx.compose.runtime  ChevronRight androidx.compose.runtime  Circle androidx.compose.runtime  CircleShape androidx.compose.runtime  CircularProgressIndicator androidx.compose.runtime  Color androidx.compose.runtime  Column androidx.compose.runtime  ComponentActivity androidx.compose.runtime  
Composable androidx.compose.runtime  ContactPhone androidx.compose.runtime  CycleOverviewCard androidx.compose.runtime  
CyclePhase androidx.compose.runtime  DailyLog androidx.compose.runtime  DarkGray androidx.compose.runtime  DateSelectionHeader androidx.compose.runtime  DateTimeFormatter androidx.compose.runtime  Divider androidx.compose.runtime  Download androidx.compose.runtime  Eco androidx.compose.runtime  Edit androidx.compose.runtime  Email androidx.compose.runtime  ErrorRed androidx.compose.runtime  Favorite androidx.compose.runtime  FertileGreen androidx.compose.runtime  FirebaseApp androidx.compose.runtime  
FlowIntensity androidx.compose.runtime  FlowIntensityButton androidx.compose.runtime  FlowIntensityChip androidx.compose.runtime  FlowIntensitySection androidx.compose.runtime  
FontWeight androidx.compose.runtime  	GridCells androidx.compose.runtime  
HeaderSection androidx.compose.runtime  Healing androidx.compose.runtime  HealthAndSafety androidx.compose.runtime  HealthInsightSeverity androidx.compose.runtime  HealthInsightsPreview androidx.compose.runtime  Help androidx.compose.runtime  Icon androidx.compose.runtime  
IconButton androidx.compose.runtime  Icons androidx.compose.runtime  ImageVector androidx.compose.runtime  Info androidx.compose.runtime  InfoBlue androidx.compose.runtime  InsightCard androidx.compose.runtime  InsightItem androidx.compose.runtime  InsightsHeader androidx.compose.runtime  Int androidx.compose.runtime  KeyboardOptions androidx.compose.runtime  KeyboardType androidx.compose.runtime  LaunchedEffect androidx.compose.runtime  
LazyColumn androidx.compose.runtime  LazyVerticalGrid androidx.compose.runtime  
LegendItem androidx.compose.runtime  	LightGray androidx.compose.runtime  	Lightbulb androidx.compose.runtime  LinearProgressIndicator androidx.compose.runtime  List androidx.compose.runtime  	LocalDate androidx.compose.runtime  Lock androidx.compose.runtime  LoggingSection androidx.compose.runtime  Logout androidx.compose.runtime  
MaterialTheme androidx.compose.runtime  Modifier androidx.compose.runtime  Mood androidx.compose.runtime  MutableState androidx.compose.runtime  Notes androidx.compose.runtime  
Notifications androidx.compose.runtime  Opacity androidx.compose.runtime  OutlinedButton androidx.compose.runtime  OutlinedTextField androidx.compose.runtime  OutlinedTextFieldDefaults androidx.compose.runtime  OverviewItem androidx.compose.runtime  OverviewTab androidx.compose.runtime  	PMSPurple androidx.compose.runtime  	PainLevel androidx.compose.runtime  
PainLevelChip androidx.compose.runtime  PainLevelSection androidx.compose.runtime  PasswordVisualTransformation androidx.compose.runtime  PatternsTab androidx.compose.runtime  PeriodPalApp androidx.compose.runtime  PeriodPalTheme androidx.compose.runtime  PeriodPalThemeColors androidx.compose.runtime  	PeriodRed androidx.compose.runtime  Person androidx.compose.runtime  	PlayArrow androidx.compose.runtime  PredictionsTab androidx.compose.runtime  Preview androidx.compose.runtime  
ProfileHeader androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  QuickActionButton androidx.compose.runtime  QuickActionButtons androidx.compose.runtime  QuickStatsSection androidx.compose.runtime  
RosePink40 androidx.compose.runtime  RoundedCornerShape androidx.compose.runtime  Row androidx.compose.runtime  Save androidx.compose.runtime  Scaffold androidx.compose.runtime  Schedule androidx.compose.runtime  Security androidx.compose.runtime  SelectedDateInfo androidx.compose.runtime  Settings androidx.compose.runtime  SettingsItem androidx.compose.runtime  SettingsItemRow androidx.compose.runtime  SettingsSection androidx.compose.runtime  
SideEffect androidx.compose.runtime  
SoftPink40 androidx.compose.runtime  
SoftPink80 androidx.compose.runtime  Spacer androidx.compose.runtime  StatItem androidx.compose.runtime  State androidx.compose.runtime  Stop androidx.compose.runtime  String androidx.compose.runtime  SuccessGreen androidx.compose.runtime  Support androidx.compose.runtime  SymptomPatternItem androidx.compose.runtime  Tab androidx.compose.runtime  TabRow androidx.compose.runtime  Text androidx.compose.runtime  	TextAlign androidx.compose.runtime  
TextButton androidx.compose.runtime  TodayLoggingSection androidx.compose.runtime  
TrendingUp androidx.compose.runtime  Unit androidx.compose.runtime  Upcoming androidx.compose.runtime  
Visibility androidx.compose.runtime  
VisibilityOff androidx.compose.runtime  VisualTransformation androidx.compose.runtime  
WarningOrange androidx.compose.runtime  	WaterDrop androidx.compose.runtime  White androidx.compose.runtime  	YearMonth androidx.compose.runtime  androidx androidx.compose.runtime  
background androidx.compose.runtime  buttonColors androidx.compose.runtime  
cardColors androidx.compose.runtime  
cardElevation androidx.compose.runtime  clip androidx.compose.runtime  collectAsState androidx.compose.runtime  colors androidx.compose.runtime  com androidx.compose.runtime  fillMaxSize androidx.compose.runtime  fillMaxWidth androidx.compose.runtime  forEach androidx.compose.runtime  forEachIndexed androidx.compose.runtime  getFlowColor androidx.compose.runtime  getPainColor androidx.compose.runtime  
getPhaseColor androidx.compose.runtime  getValue androidx.compose.runtime  height androidx.compose.runtime  ifEmpty androidx.compose.runtime  
isNotEmpty androidx.compose.runtime  lightColorScheme androidx.compose.runtime  listOf androidx.compose.runtime  mutableStateOf androidx.compose.runtime  outlinedButtonColors androidx.compose.runtime  padding androidx.compose.runtime  provideDelegate androidx.compose.runtime  remember androidx.compose.runtime  rememberCoroutineScope androidx.compose.runtime  rememberScrollState androidx.compose.runtime  setValue androidx.compose.runtime  size androidx.compose.runtime  spacedBy androidx.compose.runtime  textButtonColors androidx.compose.runtime  verticalGradient androidx.compose.runtime  verticalScroll androidx.compose.runtime  weight androidx.compose.runtime  width androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  setValue %androidx.compose.runtime.MutableState  current 3androidx.compose.runtime.ProvidableCompositionLocal  getValue androidx.compose.runtime.State  provideDelegate androidx.compose.runtime.State  compose !androidx.compose.runtime.androidx  ui )androidx.compose.runtime.androidx.compose  graphics ,androidx.compose.runtime.androidx.compose.ui  Color 5androidx.compose.runtime.androidx.compose.ui.graphics  Hamode androidx.compose.runtime.com  	periodpal #androidx.compose.runtime.com.Hamode  ui -androidx.compose.runtime.com.Hamode.periodpal  	viewmodel 0androidx.compose.runtime.com.Hamode.periodpal.ui  	AuthState :androidx.compose.runtime.com.Hamode.periodpal.ui.viewmodel  
Authenticated Dandroidx.compose.runtime.com.Hamode.periodpal.ui.viewmodel.AuthState  Loading Dandroidx.compose.runtime.com.Hamode.periodpal.ui.viewmodel.AuthState  Unauthenticated Dandroidx.compose.runtime.com.Hamode.periodpal.ui.viewmodel.AuthState  ComposableFunction0 !androidx.compose.runtime.internal  ComposableFunction1 !androidx.compose.runtime.internal  ComposableFunction2 !androidx.compose.runtime.internal  invoke 5androidx.compose.runtime.internal.ComposableFunction0  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  Center androidx.compose.ui.Alignment  CenterHorizontally androidx.compose.ui.Alignment  CenterVertically androidx.compose.ui.Alignment  	Companion androidx.compose.ui.Alignment  
Horizontal androidx.compose.ui.Alignment  Top androidx.compose.ui.Alignment  TopEnd androidx.compose.ui.Alignment  Vertical androidx.compose.ui.Alignment  Center 'androidx.compose.ui.Alignment.Companion  CenterHorizontally 'androidx.compose.ui.Alignment.Companion  CenterVertically 'androidx.compose.ui.Alignment.Companion  Top 'androidx.compose.ui.Alignment.Companion  TopEnd 'androidx.compose.ui.Alignment.Companion  	Companion androidx.compose.ui.Modifier  align androidx.compose.ui.Modifier  
background androidx.compose.ui.Modifier  border androidx.compose.ui.Modifier  	clickable androidx.compose.ui.Modifier  clip androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  fillMaxWidth androidx.compose.ui.Modifier  height androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  size androidx.compose.ui.Modifier  verticalScroll androidx.compose.ui.Modifier  weight androidx.compose.ui.Modifier  width androidx.compose.ui.Modifier  align &androidx.compose.ui.Modifier.Companion  fillMaxSize &androidx.compose.ui.Modifier.Companion  fillMaxWidth &androidx.compose.ui.Modifier.Companion  height &androidx.compose.ui.Modifier.Companion  padding &androidx.compose.ui.Modifier.Companion  size &androidx.compose.ui.Modifier.Companion  weight &androidx.compose.ui.Modifier.Companion  width &androidx.compose.ui.Modifier.Companion  clip androidx.compose.ui.draw  Brush androidx.compose.ui.graphics  Color androidx.compose.ui.graphics  toArgb androidx.compose.ui.graphics  	Companion "androidx.compose.ui.graphics.Brush  verticalGradient "androidx.compose.ui.graphics.Brush  verticalGradient ,androidx.compose.ui.graphics.Brush.Companion  	Companion "androidx.compose.ui.graphics.Color  Gray "androidx.compose.ui.graphics.Color  Transparent "androidx.compose.ui.graphics.Color  White "androidx.compose.ui.graphics.Color  copy "androidx.compose.ui.graphics.Color  toArgb "androidx.compose.ui.graphics.Color  Gray ,androidx.compose.ui.graphics.Color.Companion  Transparent ,androidx.compose.ui.graphics.Color.Companion  White ,androidx.compose.ui.graphics.Color.Companion  ImageVector #androidx.compose.ui.graphics.vector  LocalContext androidx.compose.ui.platform  	LocalView androidx.compose.ui.platform  	TextStyle androidx.compose.ui.text  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  	Companion (androidx.compose.ui.text.font.FontFamily  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  Bold (androidx.compose.ui.text.font.FontWeight  	Companion (androidx.compose.ui.text.font.FontWeight  Medium (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  SemiBold (androidx.compose.ui.text.font.FontWeight  Bold 2androidx.compose.ui.text.font.FontWeight.Companion  Medium 2androidx.compose.ui.text.font.FontWeight.Companion  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  SemiBold 2androidx.compose.ui.text.font.FontWeight.Companion  KeyboardType androidx.compose.ui.text.input  PasswordVisualTransformation androidx.compose.ui.text.input  VisualTransformation androidx.compose.ui.text.input  	Companion +androidx.compose.ui.text.input.KeyboardType  Email +androidx.compose.ui.text.input.KeyboardType  Password +androidx.compose.ui.text.input.KeyboardType  Email 5androidx.compose.ui.text.input.KeyboardType.Companion  Password 5androidx.compose.ui.text.input.KeyboardType.Companion  	Companion 3androidx.compose.ui.text.input.VisualTransformation  None 3androidx.compose.ui.text.input.VisualTransformation  None =androidx.compose.ui.text.input.VisualTransformation.Companion  	TextAlign androidx.compose.ui.text.style  Center (androidx.compose.ui.text.style.TextAlign  	Companion (androidx.compose.ui.text.style.TextAlign  Center 2androidx.compose.ui.text.style.TextAlign.Companion  Preview #androidx.compose.ui.tooling.preview  Dp androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  Bundle #androidx.core.app.ComponentActivity  FirebaseApp #androidx.core.app.ComponentActivity  PeriodPalApp #androidx.core.app.ComponentActivity  PeriodPalTheme #androidx.core.app.ComponentActivity  enableEdgeToEdge #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  WindowCompat androidx.core.view  WindowInsetsControllerCompat androidx.core.view  getInsetsController androidx.core.view.WindowCompat  isAppearanceLightStatusBars /androidx.core.view.WindowInsetsControllerCompat  	ViewModel androidx.lifecycle  viewModelScope androidx.lifecycle  	viewModel $androidx.lifecycle.viewmodel.compose  	Alignment com.Hamode.periodpal  Application com.Hamode.periodpal  Arrangement com.Hamode.periodpal  
AuthViewModel com.Hamode.periodpal  Box com.Hamode.periodpal  Brush com.Hamode.periodpal  Bundle com.Hamode.periodpal  Button com.Hamode.periodpal  ButtonDefaults com.Hamode.periodpal  Card com.Hamode.periodpal  CardDefaults com.Hamode.periodpal  CircularProgressIndicator com.Hamode.periodpal  Color com.Hamode.periodpal  Column com.Hamode.periodpal  ComponentActivity com.Hamode.periodpal  
Composable com.Hamode.periodpal  FirebaseApp com.Hamode.periodpal  
FontWeight com.Hamode.periodpal  
LoadingScreen com.Hamode.periodpal  MainActivity com.Hamode.periodpal  MainAppContent com.Hamode.periodpal  
MaterialTheme com.Hamode.periodpal  Modifier com.Hamode.periodpal  PeriodPalApp com.Hamode.periodpal  PeriodPalApplication com.Hamode.periodpal  PeriodPalMainScreen com.Hamode.periodpal  PeriodPalMainScreenPreview com.Hamode.periodpal  PeriodPalSimpleTheme com.Hamode.periodpal  PeriodPalTheme com.Hamode.periodpal  PlaceholderScreen com.Hamode.periodpal  Preview com.Hamode.periodpal  RoundedCornerShape com.Hamode.periodpal  Scaffold com.Hamode.periodpal  Spacer com.Hamode.periodpal  String com.Hamode.periodpal  Text com.Hamode.periodpal  	TextAlign com.Hamode.periodpal  Unit com.Hamode.periodpal  androidx com.Hamode.periodpal  buttonColors com.Hamode.periodpal  
cardColors com.Hamode.periodpal  
cardElevation com.Hamode.periodpal  com com.Hamode.periodpal  fillMaxSize com.Hamode.periodpal  fillMaxWidth com.Hamode.periodpal  getValue com.Hamode.periodpal  height com.Hamode.periodpal  lightColorScheme com.Hamode.periodpal  listOf com.Hamode.periodpal  mutableStateOf com.Hamode.periodpal  padding com.Hamode.periodpal  provideDelegate com.Hamode.periodpal  remember com.Hamode.periodpal  setValue com.Hamode.periodpal  spacedBy com.Hamode.periodpal  verticalGradient com.Hamode.periodpal  FirebaseApp !com.Hamode.periodpal.MainActivity  PeriodPalApp !com.Hamode.periodpal.MainActivity  PeriodPalTheme !com.Hamode.periodpal.MainActivity  enableEdgeToEdge !com.Hamode.periodpal.MainActivity  
setContent !com.Hamode.periodpal.MainActivity  FirebaseApp )com.Hamode.periodpal.PeriodPalApplication  Hamode com.Hamode.periodpal.com  	periodpal com.Hamode.periodpal.com.Hamode  ui )com.Hamode.periodpal.com.Hamode.periodpal  	viewmodel ,com.Hamode.periodpal.com.Hamode.periodpal.ui  	AuthState 6com.Hamode.periodpal.com.Hamode.periodpal.ui.viewmodel  
Authenticated @com.Hamode.periodpal.com.Hamode.periodpal.ui.viewmodel.AuthState  Loading @com.Hamode.periodpal.com.Hamode.periodpal.ui.viewmodel.AuthState  Unauthenticated @com.Hamode.periodpal.com.Hamode.periodpal.ui.viewmodel.AuthState  Add  com.Hamode.periodpal.data.models  	Alignment  com.Hamode.periodpal.data.models  	Analytics  com.Hamode.periodpal.data.models  Any  com.Hamode.periodpal.data.models  Arrangement  com.Hamode.periodpal.data.models  BarChart  com.Hamode.periodpal.data.models  Boolean  com.Hamode.periodpal.data.models  Box  com.Hamode.periodpal.data.models  Brush  com.Hamode.periodpal.data.models  Button  com.Hamode.periodpal.data.models  ButtonDefaults  com.Hamode.periodpal.data.models  CalendarDay  com.Hamode.periodpal.data.models  CalendarDayData  com.Hamode.periodpal.data.models  CalendarGrid  com.Hamode.periodpal.data.models  CalendarHeader  com.Hamode.periodpal.data.models  CalendarLegend  com.Hamode.periodpal.data.models  
CalendarToday  com.Hamode.periodpal.data.models  Card  com.Hamode.periodpal.data.models  CardDefaults  com.Hamode.periodpal.data.models  ChevronLeft  com.Hamode.periodpal.data.models  ChevronRight  com.Hamode.periodpal.data.models  Circle  com.Hamode.periodpal.data.models  CircleShape  com.Hamode.periodpal.data.models  CircularProgressIndicator  com.Hamode.periodpal.data.models  Color  com.Hamode.periodpal.data.models  Column  com.Hamode.periodpal.data.models  
Composable  com.Hamode.periodpal.data.models  CycleOverviewCard  com.Hamode.periodpal.data.models  
CyclePhase  com.Hamode.periodpal.data.models  CyclePrediction  com.Hamode.periodpal.data.models  CycleStatistics  com.Hamode.periodpal.data.models  DailyLog  com.Hamode.periodpal.data.models  DarkGray  com.Hamode.periodpal.data.models  DateSelectionHeader  com.Hamode.periodpal.data.models  DateTimeFormatter  com.Hamode.periodpal.data.models  Double  com.Hamode.periodpal.data.models  Eco  com.Hamode.periodpal.data.models  ErrorRed  com.Hamode.periodpal.data.models  	Exception  com.Hamode.periodpal.data.models  Favorite  com.Hamode.periodpal.data.models  FertileGreen  com.Hamode.periodpal.data.models  FirebaseAuth  com.Hamode.periodpal.data.models  FirebaseFirestore  com.Hamode.periodpal.data.models  FirebaseUser  com.Hamode.periodpal.data.models  
FlowIntensity  com.Hamode.periodpal.data.models  FlowIntensityButton  com.Hamode.periodpal.data.models  FlowIntensityChip  com.Hamode.periodpal.data.models  FlowIntensitySection  com.Hamode.periodpal.data.models  
FontWeight  com.Hamode.periodpal.data.models  	GridCells  com.Hamode.periodpal.data.models  
HeaderSection  com.Hamode.periodpal.data.models  Healing  com.Hamode.periodpal.data.models  HealthAndSafety  com.Hamode.periodpal.data.models  
HealthInsight  com.Hamode.periodpal.data.models  HealthInsightSeverity  com.Hamode.periodpal.data.models  HealthInsightType  com.Hamode.periodpal.data.models  HealthInsightsPreview  com.Hamode.periodpal.data.models  Icon  com.Hamode.periodpal.data.models  
IconButton  com.Hamode.periodpal.data.models  Icons  com.Hamode.periodpal.data.models  ImageVector  com.Hamode.periodpal.data.models  InfoBlue  com.Hamode.periodpal.data.models  Inject  com.Hamode.periodpal.data.models  InsightCard  com.Hamode.periodpal.data.models  InsightItem  com.Hamode.periodpal.data.models  InsightsHeader  com.Hamode.periodpal.data.models  Int  com.Hamode.periodpal.data.models  
LazyColumn  com.Hamode.periodpal.data.models  LazyVerticalGrid  com.Hamode.periodpal.data.models  
LegendItem  com.Hamode.periodpal.data.models  	LightGray  com.Hamode.periodpal.data.models  	Lightbulb  com.Hamode.periodpal.data.models  LinearProgressIndicator  com.Hamode.periodpal.data.models  List  com.Hamode.periodpal.data.models  	LocalDate  com.Hamode.periodpal.data.models  LoggingSection  com.Hamode.periodpal.data.models  Long  com.Hamode.periodpal.data.models  Map  com.Hamode.periodpal.data.models  
MaterialTheme  com.Hamode.periodpal.data.models  Modifier  com.Hamode.periodpal.data.models  Mood  com.Hamode.periodpal.data.models  	MoodState  com.Hamode.periodpal.data.models  Notes  com.Hamode.periodpal.data.models  NotificationSettings  com.Hamode.periodpal.data.models  
Notifications  com.Hamode.periodpal.data.models  Number  com.Hamode.periodpal.data.models  Opacity  com.Hamode.periodpal.data.models  OutlinedTextField  com.Hamode.periodpal.data.models  OutlinedTextFieldDefaults  com.Hamode.periodpal.data.models  OverviewItem  com.Hamode.periodpal.data.models  OverviewTab  com.Hamode.periodpal.data.models  	PMSPurple  com.Hamode.periodpal.data.models  	PainLevel  com.Hamode.periodpal.data.models  
PainLevelChip  com.Hamode.periodpal.data.models  PainLevelSection  com.Hamode.periodpal.data.models  PatternsTab  com.Hamode.periodpal.data.models  PeriodPalThemeColors  com.Hamode.periodpal.data.models  	PeriodRed  com.Hamode.periodpal.data.models  	PlayArrow  com.Hamode.periodpal.data.models  PredictionsTab  com.Hamode.periodpal.data.models  PrivacySettings  com.Hamode.periodpal.data.models  Query  com.Hamode.periodpal.data.models  QuickActionButton  com.Hamode.periodpal.data.models  QuickActionButtons  com.Hamode.periodpal.data.models  Result  com.Hamode.periodpal.data.models  
RosePink40  com.Hamode.periodpal.data.models  RoundedCornerShape  com.Hamode.periodpal.data.models  Row  com.Hamode.periodpal.data.models  Save  com.Hamode.periodpal.data.models  Schedule  com.Hamode.periodpal.data.models  SelectedDateInfo  com.Hamode.periodpal.data.models  Settings  com.Hamode.periodpal.data.models  	Singleton  com.Hamode.periodpal.data.models  
SoftPink40  com.Hamode.periodpal.data.models  
SoftPink80  com.Hamode.periodpal.data.models  Spacer  com.Hamode.periodpal.data.models  Stop  com.Hamode.periodpal.data.models  String  com.Hamode.periodpal.data.models  SuccessGreen  com.Hamode.periodpal.data.models  Symptom  com.Hamode.periodpal.data.models  SymptomPatternItem  com.Hamode.periodpal.data.models  Tab  com.Hamode.periodpal.data.models  TabRow  com.Hamode.periodpal.data.models  Text  com.Hamode.periodpal.data.models  	TextAlign  com.Hamode.periodpal.data.models  
TextButton  com.Hamode.periodpal.data.models  TodayLoggingSection  com.Hamode.periodpal.data.models  
TrendingUp  com.Hamode.periodpal.data.models  Unit  com.Hamode.periodpal.data.models  UserProfile  com.Hamode.periodpal.data.models  
WarningOrange  com.Hamode.periodpal.data.models  	WaterDrop  com.Hamode.periodpal.data.models  White  com.Hamode.periodpal.data.models  	YearMonth  com.Hamode.periodpal.data.models  androidx  com.Hamode.periodpal.data.models  average  com.Hamode.periodpal.data.models  await  com.Hamode.periodpal.data.models  
background  com.Hamode.periodpal.data.models  buttonColors  com.Hamode.periodpal.data.models  
cardColors  com.Hamode.periodpal.data.models  
cardElevation  com.Hamode.periodpal.data.models  clip  com.Hamode.periodpal.data.models  colors  com.Hamode.periodpal.data.models  dailyLogFromMap  com.Hamode.periodpal.data.models  	eachCount  com.Hamode.periodpal.data.models  	emptyList  com.Hamode.periodpal.data.models  emptyMap  com.Hamode.periodpal.data.models  failure  com.Hamode.periodpal.data.models  fillMaxSize  com.Hamode.periodpal.data.models  fillMaxWidth  com.Hamode.periodpal.data.models  filter  com.Hamode.periodpal.data.models  flatMap  com.Hamode.periodpal.data.models  forEach  com.Hamode.periodpal.data.models  forEachIndexed  com.Hamode.periodpal.data.models  fromMap  com.Hamode.periodpal.data.models  getFlowColor  com.Hamode.periodpal.data.models  getPainColor  com.Hamode.periodpal.data.models  
getPhaseColor  com.Hamode.periodpal.data.models  getValue  com.Hamode.periodpal.data.models  
groupingBy  com.Hamode.periodpal.data.models  healthInsightFromMap  com.Hamode.periodpal.data.models  height  com.Hamode.periodpal.data.models  
isNotEmpty  com.Hamode.periodpal.data.models  kotlin  com.Hamode.periodpal.data.models  let  com.Hamode.periodpal.data.models  listOf  com.Hamode.periodpal.data.models  map  com.Hamode.periodpal.data.models  
mapNotNull  com.Hamode.periodpal.data.models  mapOf  com.Hamode.periodpal.data.models  	maxOrNull  com.Hamode.periodpal.data.models  	minOrNull  com.Hamode.periodpal.data.models  
mutableListOf  com.Hamode.periodpal.data.models  mutableStateOf  com.Hamode.periodpal.data.models  padding  com.Hamode.periodpal.data.models  provideDelegate  com.Hamode.periodpal.data.models  remember  com.Hamode.periodpal.data.models  rememberScrollState  com.Hamode.periodpal.data.models  setValue  com.Hamode.periodpal.data.models  size  com.Hamode.periodpal.data.models  sortedBy  com.Hamode.periodpal.data.models  sortedByDescending  com.Hamode.periodpal.data.models  spacedBy  com.Hamode.periodpal.data.models  success  com.Hamode.periodpal.data.models  take  com.Hamode.periodpal.data.models  to  com.Hamode.periodpal.data.models  toList  com.Hamode.periodpal.data.models  toMap  com.Hamode.periodpal.data.models  verticalGradient  com.Hamode.periodpal.data.models  verticalScroll  com.Hamode.periodpal.data.models  weight  com.Hamode.periodpal.data.models  width  com.Hamode.periodpal.data.models  
FOLLICULAR +com.Hamode.periodpal.data.models.CyclePhase  LUTEAL +com.Hamode.periodpal.data.models.CyclePhase  	MENSTRUAL +com.Hamode.periodpal.data.models.CyclePhase  	OVULATION +com.Hamode.periodpal.data.models.CyclePhase  displayName +com.Hamode.periodpal.data.models.CyclePhase  cycleVariability 0com.Hamode.periodpal.data.models.CycleStatistics  copy )com.Hamode.periodpal.data.models.DailyLog  date )com.Hamode.periodpal.data.models.DailyLog  exerciseMinutes )com.Hamode.periodpal.data.models.DailyLog  
flowIntensity )com.Hamode.periodpal.data.models.DailyLog  map )com.Hamode.periodpal.data.models.DailyLog  mapOf )com.Hamode.periodpal.data.models.DailyLog  mood )com.Hamode.periodpal.data.models.DailyLog  notes )com.Hamode.periodpal.data.models.DailyLog  	painLevel )com.Hamode.periodpal.data.models.DailyLog  
sleepHours )com.Hamode.periodpal.data.models.DailyLog  symptoms )com.Hamode.periodpal.data.models.DailyLog  temperature )com.Hamode.periodpal.data.models.DailyLog  to )com.Hamode.periodpal.data.models.DailyLog  toMap )com.Hamode.periodpal.data.models.DailyLog  waterGlasses )com.Hamode.periodpal.data.models.DailyLog  weight )com.Hamode.periodpal.data.models.DailyLog  HEAVY .com.Hamode.periodpal.data.models.FlowIntensity  LIGHT .com.Hamode.periodpal.data.models.FlowIntensity  MEDIUM .com.Hamode.periodpal.data.models.FlowIntensity  NONE .com.Hamode.periodpal.data.models.FlowIntensity  SPOTTING .com.Hamode.periodpal.data.models.FlowIntensity  
VERY_HEAVY .com.Hamode.periodpal.data.models.FlowIntensity  displayName .com.Hamode.periodpal.data.models.FlowIntensity  level .com.Hamode.periodpal.data.models.FlowIntensity  name .com.Hamode.periodpal.data.models.FlowIntensity  valueOf .com.Hamode.periodpal.data.models.FlowIntensity  values .com.Hamode.periodpal.data.models.FlowIntensity  date .com.Hamode.periodpal.data.models.HealthInsight  description .com.Hamode.periodpal.data.models.HealthInsight  id .com.Hamode.periodpal.data.models.HealthInsight  isRead .com.Hamode.periodpal.data.models.HealthInsight  mapOf .com.Hamode.periodpal.data.models.HealthInsight  recommendation .com.Hamode.periodpal.data.models.HealthInsight  severity .com.Hamode.periodpal.data.models.HealthInsight  title .com.Hamode.periodpal.data.models.HealthInsight  to .com.Hamode.periodpal.data.models.HealthInsight  toMap .com.Hamode.periodpal.data.models.HealthInsight  type .com.Hamode.periodpal.data.models.HealthInsight  HIGH 6com.Hamode.periodpal.data.models.HealthInsightSeverity  INFO 6com.Hamode.periodpal.data.models.HealthInsightSeverity  LOW 6com.Hamode.periodpal.data.models.HealthInsightSeverity  MEDIUM 6com.Hamode.periodpal.data.models.HealthInsightSeverity  name 6com.Hamode.periodpal.data.models.HealthInsightSeverity  valueOf 6com.Hamode.periodpal.data.models.HealthInsightSeverity  name 2com.Hamode.periodpal.data.models.HealthInsightType  valueOf 2com.Hamode.periodpal.data.models.HealthInsightType  ANXIOUS *com.Hamode.periodpal.data.models.MoodState  CALM *com.Hamode.periodpal.data.models.MoodState  	EMOTIONAL *com.Hamode.periodpal.data.models.MoodState  	ENERGETIC *com.Hamode.periodpal.data.models.MoodState  HAPPY *com.Hamode.periodpal.data.models.MoodState  	IRRITABLE *com.Hamode.periodpal.data.models.MoodState  SAD *com.Hamode.periodpal.data.models.MoodState  TIRED *com.Hamode.periodpal.data.models.MoodState  name *com.Hamode.periodpal.data.models.MoodState  valueOf *com.Hamode.periodpal.data.models.MoodState  Any 5com.Hamode.periodpal.data.models.NotificationSettings  Boolean 5com.Hamode.periodpal.data.models.NotificationSettings  	Companion 5com.Hamode.periodpal.data.models.NotificationSettings  Map 5com.Hamode.periodpal.data.models.NotificationSettings  NotificationSettings 5com.Hamode.periodpal.data.models.NotificationSettings  String 5com.Hamode.periodpal.data.models.NotificationSettings  dailyLogReminders 5com.Hamode.periodpal.data.models.NotificationSettings  fromMap 5com.Hamode.periodpal.data.models.NotificationSettings  insightNotifications 5com.Hamode.periodpal.data.models.NotificationSettings  mapOf 5com.Hamode.periodpal.data.models.NotificationSettings  medicationReminders 5com.Hamode.periodpal.data.models.NotificationSettings  ovulationReminders 5com.Hamode.periodpal.data.models.NotificationSettings  periodReminders 5com.Hamode.periodpal.data.models.NotificationSettings  reminderTime 5com.Hamode.periodpal.data.models.NotificationSettings  to 5com.Hamode.periodpal.data.models.NotificationSettings  toMap 5com.Hamode.periodpal.data.models.NotificationSettings  NotificationSettings ?com.Hamode.periodpal.data.models.NotificationSettings.Companion  fromMap ?com.Hamode.periodpal.data.models.NotificationSettings.Companion  mapOf ?com.Hamode.periodpal.data.models.NotificationSettings.Companion  to ?com.Hamode.periodpal.data.models.NotificationSettings.Companion  EXTREME *com.Hamode.periodpal.data.models.PainLevel  MILD *com.Hamode.periodpal.data.models.PainLevel  MODERATE *com.Hamode.periodpal.data.models.PainLevel  NONE *com.Hamode.periodpal.data.models.PainLevel  SEVERE *com.Hamode.periodpal.data.models.PainLevel  displayName *com.Hamode.periodpal.data.models.PainLevel  level *com.Hamode.periodpal.data.models.PainLevel  name *com.Hamode.periodpal.data.models.PainLevel  valueOf *com.Hamode.periodpal.data.models.PainLevel  values *com.Hamode.periodpal.data.models.PainLevel  Any 0com.Hamode.periodpal.data.models.PrivacySettings  Boolean 0com.Hamode.periodpal.data.models.PrivacySettings  	Companion 0com.Hamode.periodpal.data.models.PrivacySettings  Int 0com.Hamode.periodpal.data.models.PrivacySettings  Long 0com.Hamode.periodpal.data.models.PrivacySettings  Map 0com.Hamode.periodpal.data.models.PrivacySettings  PrivacySettings 0com.Hamode.periodpal.data.models.PrivacySettings  String 0com.Hamode.periodpal.data.models.PrivacySettings  allowAnalytics 0com.Hamode.periodpal.data.models.PrivacySettings  
biometricLock 0com.Hamode.periodpal.data.models.PrivacySettings  dataRetentionMonths 0com.Hamode.periodpal.data.models.PrivacySettings  fromMap 0com.Hamode.periodpal.data.models.PrivacySettings  mapOf 0com.Hamode.periodpal.data.models.PrivacySettings  shareDataForResearch 0com.Hamode.periodpal.data.models.PrivacySettings  to 0com.Hamode.periodpal.data.models.PrivacySettings  toMap 0com.Hamode.periodpal.data.models.PrivacySettings  PrivacySettings :com.Hamode.periodpal.data.models.PrivacySettings.Companion  fromMap :com.Hamode.periodpal.data.models.PrivacySettings.Companion  mapOf :com.Hamode.periodpal.data.models.PrivacySettings.Companion  to :com.Hamode.periodpal.data.models.PrivacySettings.Companion  name (com.Hamode.periodpal.data.models.Symptom  valueOf (com.Hamode.periodpal.data.models.Symptom  Any ,com.Hamode.periodpal.data.models.UserProfile  	Companion ,com.Hamode.periodpal.data.models.UserProfile  Int ,com.Hamode.periodpal.data.models.UserProfile  	LocalDate ,com.Hamode.periodpal.data.models.UserProfile  Long ,com.Hamode.periodpal.data.models.UserProfile  Map ,com.Hamode.periodpal.data.models.UserProfile  NotificationSettings ,com.Hamode.periodpal.data.models.UserProfile  PrivacySettings ,com.Hamode.periodpal.data.models.UserProfile  String ,com.Hamode.periodpal.data.models.UserProfile  UserProfile ,com.Hamode.periodpal.data.models.UserProfile  averageCycleLength ,com.Hamode.periodpal.data.models.UserProfile  averagePeriodLength ,com.Hamode.periodpal.data.models.UserProfile  	createdAt ,com.Hamode.periodpal.data.models.UserProfile  dateOfBirth ,com.Hamode.periodpal.data.models.UserProfile  displayName ,com.Hamode.periodpal.data.models.UserProfile  email ,com.Hamode.periodpal.data.models.UserProfile  emptyMap ,com.Hamode.periodpal.data.models.UserProfile  fromMap ,com.Hamode.periodpal.data.models.UserProfile  
isNotEmpty ,com.Hamode.periodpal.data.models.UserProfile  lastPeriodStart ,com.Hamode.periodpal.data.models.UserProfile  let ,com.Hamode.periodpal.data.models.UserProfile  mapOf ,com.Hamode.periodpal.data.models.UserProfile  
notifications ,com.Hamode.periodpal.data.models.UserProfile  privacy ,com.Hamode.periodpal.data.models.UserProfile  to ,com.Hamode.periodpal.data.models.UserProfile  toMap ,com.Hamode.periodpal.data.models.UserProfile  	updatedAt ,com.Hamode.periodpal.data.models.UserProfile  userId ,com.Hamode.periodpal.data.models.UserProfile  	LocalDate 6com.Hamode.periodpal.data.models.UserProfile.Companion  NotificationSettings 6com.Hamode.periodpal.data.models.UserProfile.Companion  PrivacySettings 6com.Hamode.periodpal.data.models.UserProfile.Companion  UserProfile 6com.Hamode.periodpal.data.models.UserProfile.Companion  emptyMap 6com.Hamode.periodpal.data.models.UserProfile.Companion  fromMap 6com.Hamode.periodpal.data.models.UserProfile.Companion  
isNotEmpty 6com.Hamode.periodpal.data.models.UserProfile.Companion  let 6com.Hamode.periodpal.data.models.UserProfile.Companion  mapOf 6com.Hamode.periodpal.data.models.UserProfile.Companion  to 6com.Hamode.periodpal.data.models.UserProfile.Companion  compose )com.Hamode.periodpal.data.models.androidx  ui 1com.Hamode.periodpal.data.models.androidx.compose  graphics 4com.Hamode.periodpal.data.models.androidx.compose.ui  Color =com.Hamode.periodpal.data.models.androidx.compose.ui.graphics  Boolean $com.Hamode.periodpal.data.repository  CycleStatistics $com.Hamode.periodpal.data.repository  DailyLog $com.Hamode.periodpal.data.repository  DateTimeFormatter $com.Hamode.periodpal.data.repository  	Exception $com.Hamode.periodpal.data.repository  FirebaseAuth $com.Hamode.periodpal.data.repository  FirebaseFirestore $com.Hamode.periodpal.data.repository  FirebaseRepository $com.Hamode.periodpal.data.repository  FirebaseUser $com.Hamode.periodpal.data.repository  
FlowIntensity $com.Hamode.periodpal.data.repository  
HealthInsight $com.Hamode.periodpal.data.repository  Inject $com.Hamode.periodpal.data.repository  Int $com.Hamode.periodpal.data.repository  List $com.Hamode.periodpal.data.repository  	LocalDate $com.Hamode.periodpal.data.repository  Query $com.Hamode.periodpal.data.repository  Result $com.Hamode.periodpal.data.repository  	Singleton $com.Hamode.periodpal.data.repository  String $com.Hamode.periodpal.data.repository  Unit $com.Hamode.periodpal.data.repository  UserProfile $com.Hamode.periodpal.data.repository  average $com.Hamode.periodpal.data.repository  await $com.Hamode.periodpal.data.repository  dailyLogFromMap $com.Hamode.periodpal.data.repository  	eachCount $com.Hamode.periodpal.data.repository  failure $com.Hamode.periodpal.data.repository  filter $com.Hamode.periodpal.data.repository  flatMap $com.Hamode.periodpal.data.repository  fromMap $com.Hamode.periodpal.data.repository  
groupingBy $com.Hamode.periodpal.data.repository  healthInsightFromMap $com.Hamode.periodpal.data.repository  
isNotEmpty $com.Hamode.periodpal.data.repository  kotlin $com.Hamode.periodpal.data.repository  map $com.Hamode.periodpal.data.repository  
mapNotNull $com.Hamode.periodpal.data.repository  	maxOrNull $com.Hamode.periodpal.data.repository  	minOrNull $com.Hamode.periodpal.data.repository  
mutableListOf $com.Hamode.periodpal.data.repository  sortedBy $com.Hamode.periodpal.data.repository  sortedByDescending $com.Hamode.periodpal.data.repository  success $com.Hamode.periodpal.data.repository  take $com.Hamode.periodpal.data.repository  toList $com.Hamode.periodpal.data.repository  toMap $com.Hamode.periodpal.data.repository  CycleStatistics 7com.Hamode.periodpal.data.repository.FirebaseRepository  DateTimeFormatter 7com.Hamode.periodpal.data.repository.FirebaseRepository  	Exception 7com.Hamode.periodpal.data.repository.FirebaseRepository  
FlowIntensity 7com.Hamode.periodpal.data.repository.FirebaseRepository  Query 7com.Hamode.periodpal.data.repository.FirebaseRepository  Result 7com.Hamode.periodpal.data.repository.FirebaseRepository  Unit 7com.Hamode.periodpal.data.repository.FirebaseRepository  UserProfile 7com.Hamode.periodpal.data.repository.FirebaseRepository  auth 7com.Hamode.periodpal.data.repository.FirebaseRepository  average 7com.Hamode.periodpal.data.repository.FirebaseRepository  await 7com.Hamode.periodpal.data.repository.FirebaseRepository  calculateStatisticsFromLogs 7com.Hamode.periodpal.data.repository.FirebaseRepository  dailyLogFromMap 7com.Hamode.periodpal.data.repository.FirebaseRepository  	eachCount 7com.Hamode.periodpal.data.repository.FirebaseRepository  failure 7com.Hamode.periodpal.data.repository.FirebaseRepository  filter 7com.Hamode.periodpal.data.repository.FirebaseRepository  	firestore 7com.Hamode.periodpal.data.repository.FirebaseRepository  flatMap 7com.Hamode.periodpal.data.repository.FirebaseRepository  fromMap 7com.Hamode.periodpal.data.repository.FirebaseRepository  getCurrentUser 7com.Hamode.periodpal.data.repository.FirebaseRepository  
groupingBy 7com.Hamode.periodpal.data.repository.FirebaseRepository  healthInsightFromMap 7com.Hamode.periodpal.data.repository.FirebaseRepository  
isNotEmpty 7com.Hamode.periodpal.data.repository.FirebaseRepository  isUserLoggedIn 7com.Hamode.periodpal.data.repository.FirebaseRepository  kotlin 7com.Hamode.periodpal.data.repository.FirebaseRepository  map 7com.Hamode.periodpal.data.repository.FirebaseRepository  
mapNotNull 7com.Hamode.periodpal.data.repository.FirebaseRepository  	maxOrNull 7com.Hamode.periodpal.data.repository.FirebaseRepository  	minOrNull 7com.Hamode.periodpal.data.repository.FirebaseRepository  
mutableListOf 7com.Hamode.periodpal.data.repository.FirebaseRepository  saveUserProfile 7com.Hamode.periodpal.data.repository.FirebaseRepository  signInWithEmail 7com.Hamode.periodpal.data.repository.FirebaseRepository  signOut 7com.Hamode.periodpal.data.repository.FirebaseRepository  signUpWithEmail 7com.Hamode.periodpal.data.repository.FirebaseRepository  sortedBy 7com.Hamode.periodpal.data.repository.FirebaseRepository  sortedByDescending 7com.Hamode.periodpal.data.repository.FirebaseRepository  success 7com.Hamode.periodpal.data.repository.FirebaseRepository  take 7com.Hamode.periodpal.data.repository.FirebaseRepository  toList 7com.Hamode.periodpal.data.repository.FirebaseRepository  toMap 7com.Hamode.periodpal.data.repository.FirebaseRepository  	Alignment "com.Hamode.periodpal.ui.components  	Analytics "com.Hamode.periodpal.ui.components  Arrangement "com.Hamode.periodpal.ui.components  
Assignment "com.Hamode.periodpal.ui.components  Badge "com.Hamode.periodpal.ui.components  Boolean "com.Hamode.periodpal.ui.components  
BottomNavItem "com.Hamode.periodpal.ui.components  Box "com.Hamode.periodpal.ui.components  
CalendarMonth "com.Hamode.periodpal.ui.components  Card "com.Hamode.periodpal.ui.components  CardDefaults "com.Hamode.periodpal.ui.components  Column "com.Hamode.periodpal.ui.components  
Composable "com.Hamode.periodpal.ui.components  DarkGray "com.Hamode.periodpal.ui.components  
FontWeight "com.Hamode.periodpal.ui.components  Home "com.Hamode.periodpal.ui.components  Icon "com.Hamode.periodpal.ui.components  
IconButton "com.Hamode.periodpal.ui.components  Icons "com.Hamode.periodpal.ui.components  ImageVector "com.Hamode.periodpal.ui.components  Int "com.Hamode.periodpal.ui.components  Map "com.Hamode.periodpal.ui.components  
MaterialTheme "com.Hamode.periodpal.ui.components  Modifier "com.Hamode.periodpal.ui.components  NavigationDestination "com.Hamode.periodpal.ui.components  PeriodPalBottomNavigation "com.Hamode.periodpal.ui.components  Person "com.Hamode.periodpal.ui.components  
RosePink40 "com.Hamode.periodpal.ui.components  Row "com.Hamode.periodpal.ui.components  Text "com.Hamode.periodpal.ui.components  Unit "com.Hamode.periodpal.ui.components  align "com.Hamode.periodpal.ui.components  
cardColors "com.Hamode.periodpal.ui.components  
cardElevation "com.Hamode.periodpal.ui.components  emptyMap "com.Hamode.periodpal.ui.components  fillMaxWidth "com.Hamode.periodpal.ui.components  forEach "com.Hamode.periodpal.ui.components  getDestinationIcon "com.Hamode.periodpal.ui.components  padding "com.Hamode.periodpal.ui.components  size "com.Hamode.periodpal.ui.components  weight "com.Hamode.periodpal.ui.components  NavigationDestination "com.Hamode.periodpal.ui.navigation  SecondaryDestination "com.Hamode.periodpal.ui.navigation  String "com.Hamode.periodpal.ui.navigation  CALENDAR 8com.Hamode.periodpal.ui.navigation.NavigationDestination  HEALTH_LOGS 8com.Hamode.periodpal.ui.navigation.NavigationDestination  HOME 8com.Hamode.periodpal.ui.navigation.NavigationDestination  INSIGHTS 8com.Hamode.periodpal.ui.navigation.NavigationDestination  PROFILE 8com.Hamode.periodpal.ui.navigation.NavigationDestination  displayName 8com.Hamode.periodpal.ui.navigation.NavigationDestination  values 8com.Hamode.periodpal.ui.navigation.NavigationDestination  Add com.Hamode.periodpal.ui.screens  	Alignment com.Hamode.periodpal.ui.screens  	Analytics com.Hamode.periodpal.ui.screens  Arrangement com.Hamode.periodpal.ui.screens  
AuthScreen com.Hamode.periodpal.ui.screens  
AuthViewModel com.Hamode.periodpal.ui.screens  BarChart com.Hamode.periodpal.ui.screens  Boolean com.Hamode.periodpal.ui.screens  Box com.Hamode.periodpal.ui.screens  Brush com.Hamode.periodpal.ui.screens  Button com.Hamode.periodpal.ui.screens  ButtonDefaults com.Hamode.periodpal.ui.screens  CalendarDay com.Hamode.periodpal.ui.screens  CalendarGrid com.Hamode.periodpal.ui.screens  CalendarHeader com.Hamode.periodpal.ui.screens  CalendarLegend com.Hamode.periodpal.ui.screens  CalendarScreen com.Hamode.periodpal.ui.screens  
CalendarToday com.Hamode.periodpal.ui.screens  Card com.Hamode.periodpal.ui.screens  CardDefaults com.Hamode.periodpal.ui.screens  ChevronLeft com.Hamode.periodpal.ui.screens  ChevronRight com.Hamode.periodpal.ui.screens  Circle com.Hamode.periodpal.ui.screens  CircleShape com.Hamode.periodpal.ui.screens  CircularProgressIndicator com.Hamode.periodpal.ui.screens  Color com.Hamode.periodpal.ui.screens  Column com.Hamode.periodpal.ui.screens  
Composable com.Hamode.periodpal.ui.screens  ContactPhone com.Hamode.periodpal.ui.screens  CycleOverviewCard com.Hamode.periodpal.ui.screens  
CyclePhase com.Hamode.periodpal.ui.screens  DailyLog com.Hamode.periodpal.ui.screens  DarkGray com.Hamode.periodpal.ui.screens  DateSelectionHeader com.Hamode.periodpal.ui.screens  DateTimeFormatter com.Hamode.periodpal.ui.screens  Divider com.Hamode.periodpal.ui.screens  Download com.Hamode.periodpal.ui.screens  Eco com.Hamode.periodpal.ui.screens  Edit com.Hamode.periodpal.ui.screens  Email com.Hamode.periodpal.ui.screens  ErrorRed com.Hamode.periodpal.ui.screens  Favorite com.Hamode.periodpal.ui.screens  FertileGreen com.Hamode.periodpal.ui.screens  
FlowIntensity com.Hamode.periodpal.ui.screens  FlowIntensityButton com.Hamode.periodpal.ui.screens  FlowIntensityChip com.Hamode.periodpal.ui.screens  FlowIntensitySection com.Hamode.periodpal.ui.screens  
FontWeight com.Hamode.periodpal.ui.screens  	GridCells com.Hamode.periodpal.ui.screens  
HeaderSection com.Hamode.periodpal.ui.screens  Healing com.Hamode.periodpal.ui.screens  HealthAndSafety com.Hamode.periodpal.ui.screens  HealthInsightSeverity com.Hamode.periodpal.ui.screens  HealthInsightsPreview com.Hamode.periodpal.ui.screens  HealthInsightsScreen com.Hamode.periodpal.ui.screens  HealthLogsScreen com.Hamode.periodpal.ui.screens  Help com.Hamode.periodpal.ui.screens  
HomeScreen com.Hamode.periodpal.ui.screens  Icon com.Hamode.periodpal.ui.screens  
IconButton com.Hamode.periodpal.ui.screens  Icons com.Hamode.periodpal.ui.screens  ImageVector com.Hamode.periodpal.ui.screens  Info com.Hamode.periodpal.ui.screens  InfoBlue com.Hamode.periodpal.ui.screens  InsightCard com.Hamode.periodpal.ui.screens  InsightItem com.Hamode.periodpal.ui.screens  InsightsHeader com.Hamode.periodpal.ui.screens  Int com.Hamode.periodpal.ui.screens  KeyboardOptions com.Hamode.periodpal.ui.screens  KeyboardType com.Hamode.periodpal.ui.screens  LaunchedEffect com.Hamode.periodpal.ui.screens  
LazyColumn com.Hamode.periodpal.ui.screens  LazyVerticalGrid com.Hamode.periodpal.ui.screens  
LegendItem com.Hamode.periodpal.ui.screens  	LightGray com.Hamode.periodpal.ui.screens  	Lightbulb com.Hamode.periodpal.ui.screens  LinearProgressIndicator com.Hamode.periodpal.ui.screens  List com.Hamode.periodpal.ui.screens  	LocalDate com.Hamode.periodpal.ui.screens  Lock com.Hamode.periodpal.ui.screens  LoggingSection com.Hamode.periodpal.ui.screens  Logout com.Hamode.periodpal.ui.screens  
MaterialTheme com.Hamode.periodpal.ui.screens  Modifier com.Hamode.periodpal.ui.screens  Mood com.Hamode.periodpal.ui.screens  Notes com.Hamode.periodpal.ui.screens  
Notifications com.Hamode.periodpal.ui.screens  Opacity com.Hamode.periodpal.ui.screens  OutlinedButton com.Hamode.periodpal.ui.screens  OutlinedTextField com.Hamode.periodpal.ui.screens  OutlinedTextFieldDefaults com.Hamode.periodpal.ui.screens  OverviewItem com.Hamode.periodpal.ui.screens  OverviewTab com.Hamode.periodpal.ui.screens  	PMSPurple com.Hamode.periodpal.ui.screens  	PainLevel com.Hamode.periodpal.ui.screens  
PainLevelChip com.Hamode.periodpal.ui.screens  PainLevelSection com.Hamode.periodpal.ui.screens  PasswordVisualTransformation com.Hamode.periodpal.ui.screens  PatternsTab com.Hamode.periodpal.ui.screens  PeriodPalThemeColors com.Hamode.periodpal.ui.screens  	PeriodRed com.Hamode.periodpal.ui.screens  Person com.Hamode.periodpal.ui.screens  	PlayArrow com.Hamode.periodpal.ui.screens  PredictionsTab com.Hamode.periodpal.ui.screens  
ProfileHeader com.Hamode.periodpal.ui.screens  
ProfileScreen com.Hamode.periodpal.ui.screens  QuickActionButton com.Hamode.periodpal.ui.screens  QuickActionButtons com.Hamode.periodpal.ui.screens  QuickStatsSection com.Hamode.periodpal.ui.screens  
RosePink40 com.Hamode.periodpal.ui.screens  RoundedCornerShape com.Hamode.periodpal.ui.screens  Row com.Hamode.periodpal.ui.screens  Save com.Hamode.periodpal.ui.screens  Schedule com.Hamode.periodpal.ui.screens  Security com.Hamode.periodpal.ui.screens  SelectedDateInfo com.Hamode.periodpal.ui.screens  Settings com.Hamode.periodpal.ui.screens  SettingsItem com.Hamode.periodpal.ui.screens  SettingsItemRow com.Hamode.periodpal.ui.screens  SettingsSection com.Hamode.periodpal.ui.screens  
SoftPink40 com.Hamode.periodpal.ui.screens  
SoftPink80 com.Hamode.periodpal.ui.screens  Spacer com.Hamode.periodpal.ui.screens  StatItem com.Hamode.periodpal.ui.screens  Stop com.Hamode.periodpal.ui.screens  String com.Hamode.periodpal.ui.screens  SuccessGreen com.Hamode.periodpal.ui.screens  Support com.Hamode.periodpal.ui.screens  SymptomPatternItem com.Hamode.periodpal.ui.screens  Tab com.Hamode.periodpal.ui.screens  TabRow com.Hamode.periodpal.ui.screens  Text com.Hamode.periodpal.ui.screens  	TextAlign com.Hamode.periodpal.ui.screens  
TextButton com.Hamode.periodpal.ui.screens  TodayLoggingSection com.Hamode.periodpal.ui.screens  
TrendingUp com.Hamode.periodpal.ui.screens  Unit com.Hamode.periodpal.ui.screens  Upcoming com.Hamode.periodpal.ui.screens  
Visibility com.Hamode.periodpal.ui.screens  
VisibilityOff com.Hamode.periodpal.ui.screens  VisualTransformation com.Hamode.periodpal.ui.screens  
WarningOrange com.Hamode.periodpal.ui.screens  	WaterDrop com.Hamode.periodpal.ui.screens  White com.Hamode.periodpal.ui.screens  	YearMonth com.Hamode.periodpal.ui.screens  androidx com.Hamode.periodpal.ui.screens  
background com.Hamode.periodpal.ui.screens  buttonColors com.Hamode.periodpal.ui.screens  
cardColors com.Hamode.periodpal.ui.screens  
cardElevation com.Hamode.periodpal.ui.screens  clip com.Hamode.periodpal.ui.screens  collectAsState com.Hamode.periodpal.ui.screens  colors com.Hamode.periodpal.ui.screens  com com.Hamode.periodpal.ui.screens  fillMaxSize com.Hamode.periodpal.ui.screens  fillMaxWidth com.Hamode.periodpal.ui.screens  forEach com.Hamode.periodpal.ui.screens  forEachIndexed com.Hamode.periodpal.ui.screens  getFlowColor com.Hamode.periodpal.ui.screens  getPainColor com.Hamode.periodpal.ui.screens  
getPhaseColor com.Hamode.periodpal.ui.screens  getValue com.Hamode.periodpal.ui.screens  height com.Hamode.periodpal.ui.screens  ifEmpty com.Hamode.periodpal.ui.screens  
isNotEmpty com.Hamode.periodpal.ui.screens  listOf com.Hamode.periodpal.ui.screens  mutableStateOf com.Hamode.periodpal.ui.screens  outlinedButtonColors com.Hamode.periodpal.ui.screens  padding com.Hamode.periodpal.ui.screens  provideDelegate com.Hamode.periodpal.ui.screens  remember com.Hamode.periodpal.ui.screens  rememberCoroutineScope com.Hamode.periodpal.ui.screens  rememberScrollState com.Hamode.periodpal.ui.screens  setValue com.Hamode.periodpal.ui.screens  size com.Hamode.periodpal.ui.screens  spacedBy com.Hamode.periodpal.ui.screens  textButtonColors com.Hamode.periodpal.ui.screens  verticalGradient com.Hamode.periodpal.ui.screens  verticalScroll com.Hamode.periodpal.ui.screens  weight com.Hamode.periodpal.ui.screens  width com.Hamode.periodpal.ui.screens  icon ,com.Hamode.periodpal.ui.screens.SettingsItem  title ,com.Hamode.periodpal.ui.screens.SettingsItem  compose (com.Hamode.periodpal.ui.screens.androidx  ui 0com.Hamode.periodpal.ui.screens.androidx.compose  graphics 3com.Hamode.periodpal.ui.screens.androidx.compose.ui  Color <com.Hamode.periodpal.ui.screens.androidx.compose.ui.graphics  Hamode #com.Hamode.periodpal.ui.screens.com  	periodpal *com.Hamode.periodpal.ui.screens.com.Hamode  ui 4com.Hamode.periodpal.ui.screens.com.Hamode.periodpal  	viewmodel 7com.Hamode.periodpal.ui.screens.com.Hamode.periodpal.ui  	AuthState Acom.Hamode.periodpal.ui.screens.com.Hamode.periodpal.ui.viewmodel  
Authenticated Kcom.Hamode.periodpal.ui.screens.com.Hamode.periodpal.ui.viewmodel.AuthState  Loading Kcom.Hamode.periodpal.ui.screens.com.Hamode.periodpal.ui.viewmodel.AuthState  Unauthenticated Kcom.Hamode.periodpal.ui.screens.com.Hamode.periodpal.ui.viewmodel.AuthState  Activity com.Hamode.periodpal.ui.theme  Add com.Hamode.periodpal.ui.theme  	Alignment com.Hamode.periodpal.ui.theme  	Analytics com.Hamode.periodpal.ui.theme  Arrangement com.Hamode.periodpal.ui.theme  
Assignment com.Hamode.periodpal.ui.theme  
AuthViewModel com.Hamode.periodpal.ui.theme  Badge com.Hamode.periodpal.ui.theme  BarChart com.Hamode.periodpal.ui.theme  Black com.Hamode.periodpal.ui.theme  Boolean com.Hamode.periodpal.ui.theme  
BottomNavItem com.Hamode.periodpal.ui.theme  Box com.Hamode.periodpal.ui.theme  Brush com.Hamode.periodpal.ui.theme  Build com.Hamode.periodpal.ui.theme  Button com.Hamode.periodpal.ui.theme  ButtonDefaults com.Hamode.periodpal.ui.theme  CalendarDay com.Hamode.periodpal.ui.theme  CalendarGrid com.Hamode.periodpal.ui.theme  CalendarHeader com.Hamode.periodpal.ui.theme  CalendarLegend com.Hamode.periodpal.ui.theme  
CalendarMonth com.Hamode.periodpal.ui.theme  
CalendarToday com.Hamode.periodpal.ui.theme  Card com.Hamode.periodpal.ui.theme  CardDefaults com.Hamode.periodpal.ui.theme  ChevronLeft com.Hamode.periodpal.ui.theme  ChevronRight com.Hamode.periodpal.ui.theme  Circle com.Hamode.periodpal.ui.theme  CircleShape com.Hamode.periodpal.ui.theme  CircularProgressIndicator com.Hamode.periodpal.ui.theme  Color com.Hamode.periodpal.ui.theme  Column com.Hamode.periodpal.ui.theme  
Composable com.Hamode.periodpal.ui.theme  ContactPhone com.Hamode.periodpal.ui.theme  CycleOverviewCard com.Hamode.periodpal.ui.theme  
CyclePhase com.Hamode.periodpal.ui.theme  DailyLog com.Hamode.periodpal.ui.theme  DarkColorScheme com.Hamode.periodpal.ui.theme  DarkGray com.Hamode.periodpal.ui.theme  DateSelectionHeader com.Hamode.periodpal.ui.theme  DateTimeFormatter com.Hamode.periodpal.ui.theme  Divider com.Hamode.periodpal.ui.theme  Download com.Hamode.periodpal.ui.theme  Eco com.Hamode.periodpal.ui.theme  Edit com.Hamode.periodpal.ui.theme  Email com.Hamode.periodpal.ui.theme  ErrorRed com.Hamode.periodpal.ui.theme  Favorite com.Hamode.periodpal.ui.theme  FertileGreen com.Hamode.periodpal.ui.theme  	FlowHeavy com.Hamode.periodpal.ui.theme  
FlowIntensity com.Hamode.periodpal.ui.theme  FlowIntensityButton com.Hamode.periodpal.ui.theme  FlowIntensityChip com.Hamode.periodpal.ui.theme  FlowIntensitySection com.Hamode.periodpal.ui.theme  	FlowLight com.Hamode.periodpal.ui.theme  
FlowMedium com.Hamode.periodpal.ui.theme  FlowNone com.Hamode.periodpal.ui.theme  FlowSpotting com.Hamode.periodpal.ui.theme  
FlowVeryHeavy com.Hamode.periodpal.ui.theme  
FontFamily com.Hamode.periodpal.ui.theme  
FontWeight com.Hamode.periodpal.ui.theme  	GridCells com.Hamode.periodpal.ui.theme  
HeaderSection com.Hamode.periodpal.ui.theme  Healing com.Hamode.periodpal.ui.theme  HealthAndSafety com.Hamode.periodpal.ui.theme  HealthInsightSeverity com.Hamode.periodpal.ui.theme  HealthInsightsPreview com.Hamode.periodpal.ui.theme  Help com.Hamode.periodpal.ui.theme  HighContrastError com.Hamode.periodpal.ui.theme  HighContrastFocus com.Hamode.periodpal.ui.theme  HighContrastSuccess com.Hamode.periodpal.ui.theme  Home com.Hamode.periodpal.ui.theme  Icon com.Hamode.periodpal.ui.theme  
IconButton com.Hamode.periodpal.ui.theme  Icons com.Hamode.periodpal.ui.theme  ImageVector com.Hamode.periodpal.ui.theme  Info com.Hamode.periodpal.ui.theme  InfoBlue com.Hamode.periodpal.ui.theme  InsightCard com.Hamode.periodpal.ui.theme  InsightItem com.Hamode.periodpal.ui.theme  InsightsHeader com.Hamode.periodpal.ui.theme  Int com.Hamode.periodpal.ui.theme  KeyboardOptions com.Hamode.periodpal.ui.theme  KeyboardType com.Hamode.periodpal.ui.theme  LaunchedEffect com.Hamode.periodpal.ui.theme  
LazyColumn com.Hamode.periodpal.ui.theme  LazyVerticalGrid com.Hamode.periodpal.ui.theme  
LegendItem com.Hamode.periodpal.ui.theme  LightColorScheme com.Hamode.periodpal.ui.theme  	LightGray com.Hamode.periodpal.ui.theme  	Lightbulb com.Hamode.periodpal.ui.theme  LinearProgressIndicator com.Hamode.periodpal.ui.theme  List com.Hamode.periodpal.ui.theme  	LocalDate com.Hamode.periodpal.ui.theme  Lock com.Hamode.periodpal.ui.theme  LoggingSection com.Hamode.periodpal.ui.theme  Logout com.Hamode.periodpal.ui.theme  Map com.Hamode.periodpal.ui.theme  
MaterialTheme com.Hamode.periodpal.ui.theme  Modifier com.Hamode.periodpal.ui.theme  Mood com.Hamode.periodpal.ui.theme  MoodAnxious com.Hamode.periodpal.ui.theme  MoodCalm com.Hamode.periodpal.ui.theme  
MoodEmotional com.Hamode.periodpal.ui.theme  
MoodEnergetic com.Hamode.periodpal.ui.theme  	MoodHappy com.Hamode.periodpal.ui.theme  
MoodIrritable com.Hamode.periodpal.ui.theme  MoodSad com.Hamode.periodpal.ui.theme  	MoodTired com.Hamode.periodpal.ui.theme  NavigationDestination com.Hamode.periodpal.ui.theme  Notes com.Hamode.periodpal.ui.theme  
Notifications com.Hamode.periodpal.ui.theme  Opacity com.Hamode.periodpal.ui.theme  OutlinedButton com.Hamode.periodpal.ui.theme  OutlinedTextField com.Hamode.periodpal.ui.theme  OutlinedTextFieldDefaults com.Hamode.periodpal.ui.theme  OverviewItem com.Hamode.periodpal.ui.theme  OverviewTab com.Hamode.periodpal.ui.theme  
OvulationBlue com.Hamode.periodpal.ui.theme  	PMSPurple com.Hamode.periodpal.ui.theme  PainExtreme com.Hamode.periodpal.ui.theme  	PainLevel com.Hamode.periodpal.ui.theme  
PainLevelChip com.Hamode.periodpal.ui.theme  PainLevelSection com.Hamode.periodpal.ui.theme  PainMild com.Hamode.periodpal.ui.theme  PainModerate com.Hamode.periodpal.ui.theme  PainNone com.Hamode.periodpal.ui.theme  
PainSevere com.Hamode.periodpal.ui.theme  PasswordVisualTransformation com.Hamode.periodpal.ui.theme  PatternsTab com.Hamode.periodpal.ui.theme  PeriodPalTheme com.Hamode.periodpal.ui.theme  PeriodPalThemeColors com.Hamode.periodpal.ui.theme  	PeriodRed com.Hamode.periodpal.ui.theme  Person com.Hamode.periodpal.ui.theme  	PlayArrow com.Hamode.periodpal.ui.theme  PredictionsTab com.Hamode.periodpal.ui.theme  
ProfileHeader com.Hamode.periodpal.ui.theme  QuickActionButton com.Hamode.periodpal.ui.theme  QuickActionButtons com.Hamode.periodpal.ui.theme  QuickStatsSection com.Hamode.periodpal.ui.theme  
RosePink40 com.Hamode.periodpal.ui.theme  
RosePink80 com.Hamode.periodpal.ui.theme  RoundedCornerShape com.Hamode.periodpal.ui.theme  Row com.Hamode.periodpal.ui.theme  Save com.Hamode.periodpal.ui.theme  Schedule com.Hamode.periodpal.ui.theme  Security com.Hamode.periodpal.ui.theme  SelectedDateInfo com.Hamode.periodpal.ui.theme  Settings com.Hamode.periodpal.ui.theme  SettingsItem com.Hamode.periodpal.ui.theme  SettingsItemRow com.Hamode.periodpal.ui.theme  SettingsSection com.Hamode.periodpal.ui.theme  
SoftPink40 com.Hamode.periodpal.ui.theme  
SoftPink80 com.Hamode.periodpal.ui.theme  Spacer com.Hamode.periodpal.ui.theme  StatItem com.Hamode.periodpal.ui.theme  Stop com.Hamode.periodpal.ui.theme  String com.Hamode.periodpal.ui.theme  SuccessGreen com.Hamode.periodpal.ui.theme  Support com.Hamode.periodpal.ui.theme  SymptomPatternItem com.Hamode.periodpal.ui.theme  Tab com.Hamode.periodpal.ui.theme  TabRow com.Hamode.periodpal.ui.theme  Text com.Hamode.periodpal.ui.theme  	TextAlign com.Hamode.periodpal.ui.theme  
TextButton com.Hamode.periodpal.ui.theme  TodayLoggingSection com.Hamode.periodpal.ui.theme  
TrendingUp com.Hamode.periodpal.ui.theme  
Typography com.Hamode.periodpal.ui.theme  Unit com.Hamode.periodpal.ui.theme  Upcoming com.Hamode.periodpal.ui.theme  
Visibility com.Hamode.periodpal.ui.theme  
VisibilityOff com.Hamode.periodpal.ui.theme  VisualTransformation com.Hamode.periodpal.ui.theme  
WarningOrange com.Hamode.periodpal.ui.theme  	WaterDrop com.Hamode.periodpal.ui.theme  White com.Hamode.periodpal.ui.theme  WindowCompat com.Hamode.periodpal.ui.theme  	YearMonth com.Hamode.periodpal.ui.theme  align com.Hamode.periodpal.ui.theme  androidx com.Hamode.periodpal.ui.theme  
background com.Hamode.periodpal.ui.theme  buttonColors com.Hamode.periodpal.ui.theme  
cardColors com.Hamode.periodpal.ui.theme  
cardElevation com.Hamode.periodpal.ui.theme  clip com.Hamode.periodpal.ui.theme  collectAsState com.Hamode.periodpal.ui.theme  colors com.Hamode.periodpal.ui.theme  com com.Hamode.periodpal.ui.theme  emptyMap com.Hamode.periodpal.ui.theme  fillMaxSize com.Hamode.periodpal.ui.theme  fillMaxWidth com.Hamode.periodpal.ui.theme  forEach com.Hamode.periodpal.ui.theme  forEachIndexed com.Hamode.periodpal.ui.theme  getFlowColor com.Hamode.periodpal.ui.theme  getPainColor com.Hamode.periodpal.ui.theme  
getPhaseColor com.Hamode.periodpal.ui.theme  getValue com.Hamode.periodpal.ui.theme  height com.Hamode.periodpal.ui.theme  ifEmpty com.Hamode.periodpal.ui.theme  
isNotEmpty com.Hamode.periodpal.ui.theme  listOf com.Hamode.periodpal.ui.theme  mutableStateOf com.Hamode.periodpal.ui.theme  outlinedButtonColors com.Hamode.periodpal.ui.theme  padding com.Hamode.periodpal.ui.theme  provideDelegate com.Hamode.periodpal.ui.theme  remember com.Hamode.periodpal.ui.theme  rememberCoroutineScope com.Hamode.periodpal.ui.theme  rememberScrollState com.Hamode.periodpal.ui.theme  setValue com.Hamode.periodpal.ui.theme  size com.Hamode.periodpal.ui.theme  spacedBy com.Hamode.periodpal.ui.theme  textButtonColors com.Hamode.periodpal.ui.theme  verticalGradient com.Hamode.periodpal.ui.theme  verticalScroll com.Hamode.periodpal.ui.theme  weight com.Hamode.periodpal.ui.theme  width com.Hamode.periodpal.ui.theme  FertileGreen 2com.Hamode.periodpal.ui.theme.PeriodPalThemeColors  	FlowHeavy 2com.Hamode.periodpal.ui.theme.PeriodPalThemeColors  	FlowLight 2com.Hamode.periodpal.ui.theme.PeriodPalThemeColors  
FlowMedium 2com.Hamode.periodpal.ui.theme.PeriodPalThemeColors  FlowNone 2com.Hamode.periodpal.ui.theme.PeriodPalThemeColors  FlowSpotting 2com.Hamode.periodpal.ui.theme.PeriodPalThemeColors  
FlowVeryHeavy 2com.Hamode.periodpal.ui.theme.PeriodPalThemeColors  MoodAnxious 2com.Hamode.periodpal.ui.theme.PeriodPalThemeColors  MoodCalm 2com.Hamode.periodpal.ui.theme.PeriodPalThemeColors  
MoodEmotional 2com.Hamode.periodpal.ui.theme.PeriodPalThemeColors  
MoodEnergetic 2com.Hamode.periodpal.ui.theme.PeriodPalThemeColors  	MoodHappy 2com.Hamode.periodpal.ui.theme.PeriodPalThemeColors  
MoodIrritable 2com.Hamode.periodpal.ui.theme.PeriodPalThemeColors  MoodSad 2com.Hamode.periodpal.ui.theme.PeriodPalThemeColors  	MoodTired 2com.Hamode.periodpal.ui.theme.PeriodPalThemeColors  	PMSPurple 2com.Hamode.periodpal.ui.theme.PeriodPalThemeColors  PainExtreme 2com.Hamode.periodpal.ui.theme.PeriodPalThemeColors  PainMild 2com.Hamode.periodpal.ui.theme.PeriodPalThemeColors  PainModerate 2com.Hamode.periodpal.ui.theme.PeriodPalThemeColors  PainNone 2com.Hamode.periodpal.ui.theme.PeriodPalThemeColors  
PainSevere 2com.Hamode.periodpal.ui.theme.PeriodPalThemeColors  	PeriodRed 2com.Hamode.periodpal.ui.theme.PeriodPalThemeColors  
SoftPink40 2com.Hamode.periodpal.ui.theme.PeriodPalThemeColors  com 2com.Hamode.periodpal.ui.theme.PeriodPalThemeColors  getFlowColor 2com.Hamode.periodpal.ui.theme.PeriodPalThemeColors  getPainColor 2com.Hamode.periodpal.ui.theme.PeriodPalThemeColors  
getPhaseColor 2com.Hamode.periodpal.ui.theme.PeriodPalThemeColors  compose &com.Hamode.periodpal.ui.theme.androidx  ui .com.Hamode.periodpal.ui.theme.androidx.compose  graphics 1com.Hamode.periodpal.ui.theme.androidx.compose.ui  Color :com.Hamode.periodpal.ui.theme.androidx.compose.ui.graphics  Hamode !com.Hamode.periodpal.ui.theme.com  	periodpal (com.Hamode.periodpal.ui.theme.com.Hamode  data 2com.Hamode.periodpal.ui.theme.com.Hamode.periodpal  ui 2com.Hamode.periodpal.ui.theme.com.Hamode.periodpal  models 7com.Hamode.periodpal.ui.theme.com.Hamode.periodpal.data  
CyclePhase >com.Hamode.periodpal.ui.theme.com.Hamode.periodpal.data.models  
FlowIntensity >com.Hamode.periodpal.ui.theme.com.Hamode.periodpal.data.models  	MoodState >com.Hamode.periodpal.ui.theme.com.Hamode.periodpal.data.models  	PainLevel >com.Hamode.periodpal.ui.theme.com.Hamode.periodpal.data.models  	viewmodel 5com.Hamode.periodpal.ui.theme.com.Hamode.periodpal.ui  	AuthState ?com.Hamode.periodpal.ui.theme.com.Hamode.periodpal.ui.viewmodel  
Authenticated Icom.Hamode.periodpal.ui.theme.com.Hamode.periodpal.ui.viewmodel.AuthState  Loading Icom.Hamode.periodpal.ui.theme.com.Hamode.periodpal.ui.viewmodel.AuthState  Unauthenticated Icom.Hamode.periodpal.ui.theme.com.Hamode.periodpal.ui.viewmodel.AuthState  	AuthState !com.Hamode.periodpal.ui.viewmodel  
AuthViewModel !com.Hamode.periodpal.ui.viewmodel  FirebaseAuth !com.Hamode.periodpal.ui.viewmodel  FirebaseFirestore !com.Hamode.periodpal.ui.viewmodel  FirebaseRepository !com.Hamode.periodpal.ui.viewmodel  	LocalDate !com.Hamode.periodpal.ui.viewmodel  MutableStateFlow !com.Hamode.periodpal.ui.viewmodel  	StateFlow !com.Hamode.periodpal.ui.viewmodel  String !com.Hamode.periodpal.ui.viewmodel  	Throwable !com.Hamode.periodpal.ui.viewmodel  UserProfile !com.Hamode.periodpal.ui.viewmodel  	ViewModel !com.Hamode.periodpal.ui.viewmodel  
_authState !com.Hamode.periodpal.ui.viewmodel  
_errorMessage !com.Hamode.periodpal.ui.viewmodel  asStateFlow !com.Hamode.periodpal.ui.viewmodel  contains !com.Hamode.periodpal.ui.viewmodel  getErrorMessage !com.Hamode.periodpal.ui.viewmodel  isBlank !com.Hamode.periodpal.ui.viewmodel  launch !com.Hamode.periodpal.ui.viewmodel  	onFailure !com.Hamode.periodpal.ui.viewmodel  	onSuccess !com.Hamode.periodpal.ui.viewmodel  
repository !com.Hamode.periodpal.ui.viewmodel  	AuthState +com.Hamode.periodpal.ui.viewmodel.AuthState  
Authenticated +com.Hamode.periodpal.ui.viewmodel.AuthState  Loading +com.Hamode.periodpal.ui.viewmodel.AuthState  Unauthenticated +com.Hamode.periodpal.ui.viewmodel.AuthState  	AuthState /com.Hamode.periodpal.ui.viewmodel.AuthViewModel  	LocalDate /com.Hamode.periodpal.ui.viewmodel.AuthViewModel  MutableStateFlow /com.Hamode.periodpal.ui.viewmodel.AuthViewModel  UserProfile /com.Hamode.periodpal.ui.viewmodel.AuthViewModel  
_authState /com.Hamode.periodpal.ui.viewmodel.AuthViewModel  
_errorMessage /com.Hamode.periodpal.ui.viewmodel.AuthViewModel  asStateFlow /com.Hamode.periodpal.ui.viewmodel.AuthViewModel  	authState /com.Hamode.periodpal.ui.viewmodel.AuthViewModel  checkAuthState /com.Hamode.periodpal.ui.viewmodel.AuthViewModel  
clearError /com.Hamode.periodpal.ui.viewmodel.AuthViewModel  contains /com.Hamode.periodpal.ui.viewmodel.AuthViewModel  errorMessage /com.Hamode.periodpal.ui.viewmodel.AuthViewModel  getErrorMessage /com.Hamode.periodpal.ui.viewmodel.AuthViewModel  isBlank /com.Hamode.periodpal.ui.viewmodel.AuthViewModel  launch /com.Hamode.periodpal.ui.viewmodel.AuthViewModel  	onFailure /com.Hamode.periodpal.ui.viewmodel.AuthViewModel  	onSuccess /com.Hamode.periodpal.ui.viewmodel.AuthViewModel  
repository /com.Hamode.periodpal.ui.viewmodel.AuthViewModel  signIn /com.Hamode.periodpal.ui.viewmodel.AuthViewModel  signOut /com.Hamode.periodpal.ui.viewmodel.AuthViewModel  signUp /com.Hamode.periodpal.ui.viewmodel.AuthViewModel  viewModelScope /com.Hamode.periodpal.ui.viewmodel.AuthViewModel  Task com.google.android.gms.tasks  await !com.google.android.gms.tasks.Task  FirebaseApp com.google.firebase  
initializeApp com.google.firebase.FirebaseApp  FirebaseAuth com.google.firebase.auth  FirebaseUser com.google.firebase.auth  user #com.google.firebase.auth.AuthResult  createUserWithEmailAndPassword %com.google.firebase.auth.FirebaseAuth  currentUser %com.google.firebase.auth.FirebaseAuth  getInstance %com.google.firebase.auth.FirebaseAuth  signInWithEmailAndPassword %com.google.firebase.auth.FirebaseAuth  signOut %com.google.firebase.auth.FirebaseAuth  uid %com.google.firebase.auth.FirebaseUser  CollectionReference com.google.firebase.firestore  DocumentReference com.google.firebase.firestore  FirebaseFirestore com.google.firebase.firestore  Query com.google.firebase.firestore  document 1com.google.firebase.firestore.CollectionReference  orderBy 1com.google.firebase.firestore.CollectionReference  whereGreaterThanOrEqualTo 1com.google.firebase.firestore.CollectionReference  
collection /com.google.firebase.firestore.DocumentReference  get /com.google.firebase.firestore.DocumentReference  set /com.google.firebase.firestore.DocumentReference  data .com.google.firebase.firestore.DocumentSnapshot  exists .com.google.firebase.firestore.DocumentSnapshot  
collection /com.google.firebase.firestore.FirebaseFirestore  getInstance /com.google.firebase.firestore.FirebaseFirestore  	Direction #com.google.firebase.firestore.Query  get #com.google.firebase.firestore.Query  limit #com.google.firebase.firestore.Query  orderBy #com.google.firebase.firestore.Query  whereGreaterThanOrEqualTo #com.google.firebase.firestore.Query  whereLessThanOrEqualTo #com.google.firebase.firestore.Query  
DESCENDING -com.google.firebase.firestore.Query.Direction  	documents +com.google.firebase.firestore.QuerySnapshot  	Exception 	java.lang  	LocalDate 	java.time  	YearMonth 	java.time  value java.time.DayOfWeek  
dayOfMonth java.time.LocalDate  	dayOfWeek java.time.LocalDate  format java.time.LocalDate  isAfter java.time.LocalDate  	minusDays java.time.LocalDate  now java.time.LocalDate  parse java.time.LocalDate  plusDays java.time.LocalDate  
toEpochDay java.time.LocalDate  toString java.time.LocalDate  atDay java.time.YearMonth  format java.time.YearMonth  
lengthOfMonth java.time.YearMonth  minusMonths java.time.YearMonth  now java.time.YearMonth  
plusMonths java.time.YearMonth  DateTimeFormatter java.time.format  ISO_LOCAL_DATE "java.time.format.DateTimeFormatter  	ofPattern "java.time.format.DateTimeFormatter  Inject javax.inject  	Singleton javax.inject  Array kotlin  CharSequence kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  Nothing kotlin  Number kotlin  Pair kotlin  Result kotlin  	Throwable kotlin  let kotlin  map kotlin  	onFailure kotlin  	onSuccess kotlin  to kotlin  toList kotlin  forEach kotlin.Array  not kotlin.Boolean  	compareTo 
kotlin.Double  sp 
kotlin.Double  Int kotlin.Enum  String kotlin.Enum  invoke kotlin.Function0  invoke kotlin.Function1  	compareTo 
kotlin.Int  div 
kotlin.Int  minus 
kotlin.Int  plus 
kotlin.Int  rangeTo 
kotlin.Int  rem 
kotlin.Int  toLong 
kotlin.Int  toString 
kotlin.Int  minus kotlin.Long  toInt kotlin.Long  toDouble 
kotlin.Number  toInt 
kotlin.Number  first kotlin.Pair  second kotlin.Pair  	Companion 
kotlin.Result  failure 
kotlin.Result  	onFailure 
kotlin.Result  	onSuccess 
kotlin.Result  success 
kotlin.Result  failure kotlin.Result.Companion  success kotlin.Result.Companion  contains 
kotlin.String  ifEmpty 
kotlin.String  isBlank 
kotlin.String  
isNotEmpty 
kotlin.String  length 
kotlin.String  let 
kotlin.String  to 
kotlin.String  message kotlin.Throwable  Grouping kotlin.collections  Iterable kotlin.collections  Iterator kotlin.collections  List kotlin.collections  Map kotlin.collections  MutableList kotlin.collections  average kotlin.collections  contains kotlin.collections  	eachCount kotlin.collections  	emptyList kotlin.collections  emptyMap kotlin.collections  filter kotlin.collections  flatMap kotlin.collections  forEach kotlin.collections  forEachIndexed kotlin.collections  
groupingBy kotlin.collections  ifEmpty kotlin.collections  
isNotEmpty kotlin.collections  listOf kotlin.collections  map kotlin.collections  
mapNotNull kotlin.collections  mapOf kotlin.collections  	maxOrNull kotlin.collections  	minOrNull kotlin.collections  
mutableListOf kotlin.collections  sortedBy kotlin.collections  sortedByDescending kotlin.collections  take kotlin.collections  toList kotlin.collections  	eachCount kotlin.collections.Grouping  hasNext kotlin.collections.Iterator  next kotlin.collections.Iterator  average kotlin.collections.List  filter kotlin.collections.List  flatMap kotlin.collections.List  forEachIndexed kotlin.collections.List  
groupingBy kotlin.collections.List  iterator kotlin.collections.List  map kotlin.collections.List  
mapNotNull kotlin.collections.List  size kotlin.collections.List  sortedBy kotlin.collections.List  sortedByDescending kotlin.collections.List  take kotlin.collections.List  get kotlin.collections.Map  toList kotlin.collections.Map  add kotlin.collections.MutableList  average kotlin.collections.MutableList  
isNotEmpty kotlin.collections.MutableList  map kotlin.collections.MutableList  	maxOrNull kotlin.collections.MutableList  	minOrNull kotlin.collections.MutableList  size kotlin.collections.MutableList  SuspendFunction1 kotlin.coroutines  kotlin 
kotlin.jvm  abs kotlin.math  IntRange 
kotlin.ranges  	LongRange 
kotlin.ranges  contains 
kotlin.ranges  contains kotlin.ranges.IntRange  KClass kotlin.reflect  KMutableProperty0 kotlin.reflect  
KProperty0 kotlin.reflect  Sequence kotlin.sequences  average kotlin.sequences  contains kotlin.sequences  filter kotlin.sequences  flatMap kotlin.sequences  forEach kotlin.sequences  forEachIndexed kotlin.sequences  
groupingBy kotlin.sequences  ifEmpty kotlin.sequences  map kotlin.sequences  
mapNotNull kotlin.sequences  	maxOrNull kotlin.sequences  	minOrNull kotlin.sequences  sortedBy kotlin.sequences  sortedByDescending kotlin.sequences  take kotlin.sequences  toList kotlin.sequences  contains kotlin.text  filter kotlin.text  flatMap kotlin.text  forEach kotlin.text  forEachIndexed kotlin.text  
groupingBy kotlin.text  ifEmpty kotlin.text  isBlank kotlin.text  
isNotEmpty kotlin.text  map kotlin.text  
mapNotNull kotlin.text  	maxOrNull kotlin.text  	minOrNull kotlin.text  take kotlin.text  toList kotlin.text  CoroutineScope kotlinx.coroutines  Job kotlinx.coroutines  launch kotlinx.coroutines  	AuthState !kotlinx.coroutines.CoroutineScope  	LocalDate !kotlinx.coroutines.CoroutineScope  UserProfile !kotlinx.coroutines.CoroutineScope  
_authState !kotlinx.coroutines.CoroutineScope  
_errorMessage !kotlinx.coroutines.CoroutineScope  getErrorMessage !kotlinx.coroutines.CoroutineScope  launch !kotlinx.coroutines.CoroutineScope  	onFailure !kotlinx.coroutines.CoroutineScope  	onSuccess !kotlinx.coroutines.CoroutineScope  
repository !kotlinx.coroutines.CoroutineScope  Flow kotlinx.coroutines.flow  MutableStateFlow kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  asStateFlow kotlinx.coroutines.flow  flow kotlinx.coroutines.flow  asStateFlow (kotlinx.coroutines.flow.MutableStateFlow  value (kotlinx.coroutines.flow.MutableStateFlow  collectAsState !kotlinx.coroutines.flow.StateFlow  await kotlinx.coroutines.tasks  FirebaseTestUtils android.app.Activity  testFirebaseConnection android.app.Activity  FirebaseTestUtils android.content.Context  testFirebaseConnection android.content.Context  FirebaseTestUtils android.content.ContextWrapper  testFirebaseConnection android.content.ContextWrapper  Log android.util  d android.util.Log  e android.util.Log  FirebaseTestUtils  android.view.ContextThemeWrapper  testFirebaseConnection  android.view.ContextThemeWrapper  FirebaseTestUtils #androidx.activity.ComponentActivity  testFirebaseConnection #androidx.activity.ComponentActivity  FirebaseTestUtils "androidx.compose.foundation.layout  testFirebaseConnection "androidx.compose.foundation.layout  FirebaseTestUtils androidx.compose.material3  testFirebaseConnection androidx.compose.material3  FirebaseTestUtils androidx.compose.runtime  testFirebaseConnection androidx.compose.runtime  FirebaseTestUtils #androidx.core.app.ComponentActivity  testFirebaseConnection #androidx.core.app.ComponentActivity  FirebaseTestUtils com.Hamode.periodpal  testFirebaseConnection com.Hamode.periodpal  FirebaseTestUtils !com.Hamode.periodpal.MainActivity  testFirebaseConnection !com.Hamode.periodpal.MainActivity  	Exception com.Hamode.periodpal.utils  FirebaseAuth com.Hamode.periodpal.utils  FirebaseFirestore com.Hamode.periodpal.utils  FirebaseTestUtils com.Hamode.periodpal.utils  Log com.Hamode.periodpal.utils  System com.Hamode.periodpal.utils  mapOf com.Hamode.periodpal.utils  to com.Hamode.periodpal.utils  FirebaseAuth ,com.Hamode.periodpal.utils.FirebaseTestUtils  FirebaseFirestore ,com.Hamode.periodpal.utils.FirebaseTestUtils  Log ,com.Hamode.periodpal.utils.FirebaseTestUtils  System ,com.Hamode.periodpal.utils.FirebaseTestUtils  TAG ,com.Hamode.periodpal.utils.FirebaseTestUtils  mapOf ,com.Hamode.periodpal.utils.FirebaseTestUtils  testFirebaseConnection ,com.Hamode.periodpal.utils.FirebaseTestUtils  to ,com.Hamode.periodpal.utils.FirebaseTestUtils  OnFailureListener com.google.android.gms.tasks  OnSuccessListener com.google.android.gms.tasks  <SAM-CONSTRUCTOR> .com.google.android.gms.tasks.OnFailureListener  <SAM-CONSTRUCTOR> .com.google.android.gms.tasks.OnSuccessListener  addOnFailureListener !com.google.android.gms.tasks.Task  addOnSuccessListener !com.google.android.gms.tasks.Task  name com.google.firebase.FirebaseApp  app %com.google.firebase.auth.FirebaseAuth  app /com.google.firebase.firestore.FirebaseFirestore  currentTimeMillis java.lang.System  	ArrowBack "androidx.compose.foundation.layout  	Bloodtype "androidx.compose.foundation.layout  	Exception "androidx.compose.foundation.layout  ExperimentalMaterial3Api "androidx.compose.foundation.layout  OptIn "androidx.compose.foundation.layout  ProfileViewModel "androidx.compose.foundation.layout  UserProfile "androidx.compose.foundation.layout  
isNotBlank "androidx.compose.foundation.layout  kotlinx "androidx.compose.foundation.layout  let "androidx.compose.foundation.layout  toIntOrNull "androidx.compose.foundation.layout  trim "androidx.compose.foundation.layout  
RosePink40 +androidx.compose.foundation.layout.BoxScope  	ArrowBack .androidx.compose.foundation.layout.ColumnScope  	Bloodtype .androidx.compose.foundation.layout.ColumnScope  
isNotBlank .androidx.compose.foundation.layout.ColumnScope  let .androidx.compose.foundation.layout.ColumnScope  toIntOrNull .androidx.compose.foundation.layout.ColumnScope  trim .androidx.compose.foundation.layout.ColumnScope  	ArrowBack +androidx.compose.foundation.layout.RowScope  data 7androidx.compose.foundation.layout.com.Hamode.periodpal  models <androidx.compose.foundation.layout.com.Hamode.periodpal.data  CycleStatistics Candroidx.compose.foundation.layout.com.Hamode.periodpal.data.models  	ArrowBack ,androidx.compose.material.icons.Icons.Filled  	Bloodtype ,androidx.compose.material.icons.Icons.Filled  	ArrowBack &androidx.compose.material.icons.filled  	Bloodtype &androidx.compose.material.icons.filled  	Exception &androidx.compose.material.icons.filled  ExperimentalMaterial3Api &androidx.compose.material.icons.filled  OptIn &androidx.compose.material.icons.filled  ProfileViewModel &androidx.compose.material.icons.filled  UserProfile &androidx.compose.material.icons.filled  
isNotBlank &androidx.compose.material.icons.filled  kotlinx &androidx.compose.material.icons.filled  let &androidx.compose.material.icons.filled  toIntOrNull &androidx.compose.material.icons.filled  trim &androidx.compose.material.icons.filled  data ;androidx.compose.material.icons.filled.com.Hamode.periodpal  models @androidx.compose.material.icons.filled.com.Hamode.periodpal.data  CycleStatistics Gandroidx.compose.material.icons.filled.com.Hamode.periodpal.data.models  	ArrowBack androidx.compose.material3  	Bloodtype androidx.compose.material3  	Exception androidx.compose.material3  ExperimentalMaterial3Api androidx.compose.material3  OptIn androidx.compose.material3  ProfileViewModel androidx.compose.material3  UserProfile androidx.compose.material3  
isNotBlank androidx.compose.material3  kotlinx androidx.compose.material3  let androidx.compose.material3  toIntOrNull androidx.compose.material3  trim androidx.compose.material3  onBackground &androidx.compose.material3.ColorScheme  data /androidx.compose.material3.com.Hamode.periodpal  models 4androidx.compose.material3.com.Hamode.periodpal.data  CycleStatistics ;androidx.compose.material3.com.Hamode.periodpal.data.models  	ArrowBack androidx.compose.runtime  	Bloodtype androidx.compose.runtime  	Exception androidx.compose.runtime  ExperimentalMaterial3Api androidx.compose.runtime  OptIn androidx.compose.runtime  ProfileViewModel androidx.compose.runtime  UserProfile androidx.compose.runtime  
isNotBlank androidx.compose.runtime  kotlinx androidx.compose.runtime  let androidx.compose.runtime  toIntOrNull androidx.compose.runtime  trim androidx.compose.runtime  data -androidx.compose.runtime.com.Hamode.periodpal  models 2androidx.compose.runtime.com.Hamode.periodpal.data  CycleStatistics 9androidx.compose.runtime.com.Hamode.periodpal.data.models  Number +androidx.compose.ui.text.input.KeyboardType  Number 5androidx.compose.ui.text.input.KeyboardType.Companion  copy ,com.Hamode.periodpal.data.models.UserProfile  calculateCycleStatistics 7com.Hamode.periodpal.data.repository.FirebaseRepository  getUserProfile 7com.Hamode.periodpal.data.repository.FirebaseRepository  	ArrowBack com.Hamode.periodpal.ui.screens  	Bloodtype com.Hamode.periodpal.ui.screens  EditProfileScreen com.Hamode.periodpal.ui.screens  	Exception com.Hamode.periodpal.ui.screens  ExperimentalMaterial3Api com.Hamode.periodpal.ui.screens  OptIn com.Hamode.periodpal.ui.screens  ProfileViewModel com.Hamode.periodpal.ui.screens  UserProfile com.Hamode.periodpal.ui.screens  
isNotBlank com.Hamode.periodpal.ui.screens  kotlinx com.Hamode.periodpal.ui.screens  let com.Hamode.periodpal.ui.screens  toIntOrNull com.Hamode.periodpal.ui.screens  trim com.Hamode.periodpal.ui.screens  data 4com.Hamode.periodpal.ui.screens.com.Hamode.periodpal  models 9com.Hamode.periodpal.ui.screens.com.Hamode.periodpal.data  CycleStatistics @com.Hamode.periodpal.ui.screens.com.Hamode.periodpal.data.models  	ArrowBack com.Hamode.periodpal.ui.theme  	Bloodtype com.Hamode.periodpal.ui.theme  	Exception com.Hamode.periodpal.ui.theme  ExperimentalMaterial3Api com.Hamode.periodpal.ui.theme  OptIn com.Hamode.periodpal.ui.theme  ProfileViewModel com.Hamode.periodpal.ui.theme  UserProfile com.Hamode.periodpal.ui.theme  
isNotBlank com.Hamode.periodpal.ui.theme  kotlinx com.Hamode.periodpal.ui.theme  let com.Hamode.periodpal.ui.theme  toIntOrNull com.Hamode.periodpal.ui.theme  trim com.Hamode.periodpal.ui.theme  CycleStatistics >com.Hamode.periodpal.ui.theme.com.Hamode.periodpal.data.models  Boolean !com.Hamode.periodpal.ui.viewmodel  
ChronoUnit !com.Hamode.periodpal.ui.viewmodel  CycleStatistics !com.Hamode.periodpal.ui.viewmodel  Int !com.Hamode.periodpal.ui.viewmodel  ProfileUiState !com.Hamode.periodpal.ui.viewmodel  ProfileViewModel !com.Hamode.periodpal.ui.viewmodel  _cycleStatistics !com.Hamode.periodpal.ui.viewmodel  _uiState !com.Hamode.periodpal.ui.viewmodel  _userProfile !com.Hamode.periodpal.ui.viewmodel  println !com.Hamode.periodpal.ui.viewmodel  copy 0com.Hamode.periodpal.ui.viewmodel.ProfileUiState  errorMessage 0com.Hamode.periodpal.ui.viewmodel.ProfileUiState  	isLoading 0com.Hamode.periodpal.ui.viewmodel.ProfileUiState  showSuccessMessage 0com.Hamode.periodpal.ui.viewmodel.ProfileUiState  
ChronoUnit 2com.Hamode.periodpal.ui.viewmodel.ProfileViewModel  	LocalDate 2com.Hamode.periodpal.ui.viewmodel.ProfileViewModel  MutableStateFlow 2com.Hamode.periodpal.ui.viewmodel.ProfileViewModel  ProfileUiState 2com.Hamode.periodpal.ui.viewmodel.ProfileViewModel  _cycleStatistics 2com.Hamode.periodpal.ui.viewmodel.ProfileViewModel  _uiState 2com.Hamode.periodpal.ui.viewmodel.ProfileViewModel  _userProfile 2com.Hamode.periodpal.ui.viewmodel.ProfileViewModel  asStateFlow 2com.Hamode.periodpal.ui.viewmodel.ProfileViewModel  clearSuccessMessage 2com.Hamode.periodpal.ui.viewmodel.ProfileViewModel  cycleStatistics 2com.Hamode.periodpal.ui.viewmodel.ProfileViewModel  getNextPeriodDays 2com.Hamode.periodpal.ui.viewmodel.ProfileViewModel  launch 2com.Hamode.periodpal.ui.viewmodel.ProfileViewModel  loadCycleStatistics 2com.Hamode.periodpal.ui.viewmodel.ProfileViewModel  loadUserProfile 2com.Hamode.periodpal.ui.viewmodel.ProfileViewModel  	onFailure 2com.Hamode.periodpal.ui.viewmodel.ProfileViewModel  	onSuccess 2com.Hamode.periodpal.ui.viewmodel.ProfileViewModel  println 2com.Hamode.periodpal.ui.viewmodel.ProfileViewModel  
repository 2com.Hamode.periodpal.ui.viewmodel.ProfileViewModel  signOut 2com.Hamode.periodpal.ui.viewmodel.ProfileViewModel  uiState 2com.Hamode.periodpal.ui.viewmodel.ProfileViewModel  updateUserProfile 2com.Hamode.periodpal.ui.viewmodel.ProfileViewModel  userProfile 2com.Hamode.periodpal.ui.viewmodel.ProfileViewModel  viewModelScope 2com.Hamode.periodpal.ui.viewmodel.ProfileViewModel  
ChronoUnit java.time.temporal  DAYS java.time.temporal.ChronoUnit  between java.time.temporal.ChronoUnit  OptIn kotlin  
unaryMinus 
kotlin.Int  
isNotBlank 
kotlin.String  toIntOrNull 
kotlin.String  trim 
kotlin.String  println 	kotlin.io  
isNotBlank kotlin.text  toIntOrNull kotlin.text  trim kotlin.text  delay kotlinx.coroutines  DateTimeFormatter !kotlinx.coroutines.CoroutineScope  _cycleStatistics !kotlinx.coroutines.CoroutineScope  _uiState !kotlinx.coroutines.CoroutineScope  _userProfile !kotlinx.coroutines.CoroutineScope  kotlinx !kotlinx.coroutines.CoroutineScope  let !kotlinx.coroutines.CoroutineScope  println !kotlinx.coroutines.CoroutineScope  	clickable "androidx.compose.foundation.layout  	clickable .androidx.compose.foundation.layout.ColumnScope  	clickable &androidx.compose.material.icons.filled  	clickable androidx.compose.material3  	clickable androidx.compose.runtime  	clickable com.Hamode.periodpal.ui.screens  onClick ,com.Hamode.periodpal.ui.screens.SettingsItem  	clickable com.Hamode.periodpal.ui.theme  Refresh "androidx.compose.foundation.layout  Refresh .androidx.compose.foundation.layout.ColumnScope  OutlinedButton +androidx.compose.foundation.layout.RowScope  Refresh +androidx.compose.foundation.layout.RowScope  androidx +androidx.compose.foundation.layout.RowScope  outlinedButtonColors +androidx.compose.foundation.layout.RowScope  Refresh ,androidx.compose.material.icons.Icons.Filled  Refresh &androidx.compose.material.icons.filled  Refresh androidx.compose.material3  Refresh androidx.compose.runtime  averageCycleLength 0com.Hamode.periodpal.data.models.CycleStatistics  averagePeriodLength 0com.Hamode.periodpal.data.models.CycleStatistics  getRegularityDescription 0com.Hamode.periodpal.data.models.CycleStatistics  totalCycles 0com.Hamode.periodpal.data.models.CycleStatistics  getDailyLogsInRange 7com.Hamode.periodpal.data.repository.FirebaseRepository  Refresh com.Hamode.periodpal.ui.screens  Refresh com.Hamode.periodpal.ui.theme  Any !com.Hamode.periodpal.ui.viewmodel  	Exception !com.Hamode.periodpal.ui.viewmodel  List !com.Hamode.periodpal.ui.viewmodel  com !com.Hamode.periodpal.ui.viewmodel  let !com.Hamode.periodpal.ui.viewmodel  map !com.Hamode.periodpal.ui.viewmodel  mapOf !com.Hamode.periodpal.ui.viewmodel  mutableMapOf !com.Hamode.periodpal.ui.viewmodel  set !com.Hamode.periodpal.ui.viewmodel  to !com.Hamode.periodpal.ui.viewmodel  toMap !com.Hamode.periodpal.ui.viewmodel  exportUserData 2com.Hamode.periodpal.ui.viewmodel.ProfileViewModel  let 2com.Hamode.periodpal.ui.viewmodel.ProfileViewModel  map 2com.Hamode.periodpal.ui.viewmodel.ProfileViewModel  mapOf 2com.Hamode.periodpal.ui.viewmodel.ProfileViewModel  mutableMapOf 2com.Hamode.periodpal.ui.viewmodel.ProfileViewModel  refreshData 2com.Hamode.periodpal.ui.viewmodel.ProfileViewModel  set 2com.Hamode.periodpal.ui.viewmodel.ProfileViewModel  to 2com.Hamode.periodpal.ui.viewmodel.ProfileViewModel  toMap 2com.Hamode.periodpal.ui.viewmodel.ProfileViewModel  Hamode %com.Hamode.periodpal.ui.viewmodel.com  	periodpal ,com.Hamode.periodpal.ui.viewmodel.com.Hamode  data 6com.Hamode.periodpal.ui.viewmodel.com.Hamode.periodpal  models ;com.Hamode.periodpal.ui.viewmodel.com.Hamode.periodpal.data  DailyLog Bcom.Hamode.periodpal.ui.viewmodel.com.Hamode.periodpal.data.models  message java.lang.Exception  
MutableMap kotlin.collections  mutableMapOf kotlin.collections  set kotlin.collections  set kotlin.collections.MutableMap  set kotlin.text  map !kotlinx.coroutines.CoroutineScope  mapOf !kotlinx.coroutines.CoroutineScope  mutableMapOf !kotlinx.coroutines.CoroutineScope  set !kotlinx.coroutines.CoroutineScope  to !kotlinx.coroutines.CoroutineScope  toMap !kotlinx.coroutines.CoroutineScope                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             